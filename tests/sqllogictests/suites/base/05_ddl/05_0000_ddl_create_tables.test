statement ok
DROP TABLE IF EXISTS t

statement ok
DROP TABLE IF EXISTS t2

statement ok
DROP TABLE IF EXISTS t3

statement ok
DROP TABLE IF EXISTS t4

statement error 1302
CREATE TABLE IF NOT EXISTS t(c1 int) ENGINE = iceberg

statement ok
CREATE TABLE t(c1 int) ENGINE = Null

statement ok
CREATE TABLE IF NOT EXISTS t(c1 int) ENGINE = Null

statement error 2302
CREATE TABLE t(c1 int) ENGINE = Null

statement error 1074
CREATE TABLE IF NOT EXISTS members (name VARCHAR, age INT) STORAGE_FORMAT = 'abc'

statement error 1074
CREATE TABLE members (name VARCHAR, age INT) STORAGE_FORMAT = 'abc'

statement ok
create table t2(a int,b int) Engine = Fuse

statement ok
insert into t2 values(1,1),(2,2)

query I
select a+b from t2
----
2
4

statement error 2302
create table t2(a int,b int) Engine = Fuse

statement error 2302
create table t2(a int,b int) Engine = Fuse

statement error 1005
create table t2(a INT auto_increment)

statement error 2703
create table t3(a int,b int) engine=Memory CLUSTER BY(a)

statement ok
create table t3(`a` int) ENGINE = Null

statement ok
create table t4(a int) ENGINE = Null

statement ok
DROP TABLE IF EXISTS t

statement ok
DROP TABLE IF EXISTS t2

statement ok
DROP TABLE IF EXISTS t3

statement ok
DROP TABLE IF EXISTS t4

statement ok
DROP DATABASE IF EXISTS db1

statement ok
DROP DATABASE IF EXISTS db2

statement ok
CREATE DATABASE db1

statement ok
CREATE DATABASE db2

statement ok
CREATE TABLE db1.test1(a INT not null, b INT null) Engine = Fuse

statement ok
INSERT INTO db1.test1 VALUES (1, 2), (2, 3), (3, 4)

statement ok
CREATE TABLE db2.test2 LIKE db1.test1 ENGINE=fuse

statement ok
INSERT INTO db2.test2 VALUES (3, 5)

query I
SELECT a+b FROM db2.test2
----
8

query TTTTT
DESCRIBE db2.test2
----
a INT NO 0 (empty)
b INT YES NULL (empty)

statement ok
CREATE TABLE db2.test3(a Varchar null, y Varchar null) ENGINE=fuse AS SELECT * FROM db1.test1

query TTTTT
DESCRIBE db2.test3
----
a VARCHAR YES NULL (empty)
y VARCHAR YES NULL (empty)

query T
SELECT a FROM db2.test3
----
1
2
3

statement ok
CREATE TABLE db2.test4(a Varchar null, y Varchar null) ENGINE=fuse AS SELECT b, a FROM db1.test1

statement ok
CREATE TABLE if not exists db2.test4 AS SELECT b, a FROM db1.test1

statement ok
CREATE TABLE if not exists db2.test4 AS SELECT b, a FROM db1.test1

query TTTTT
DESCRIBE db2.test4
----
a VARCHAR YES NULL (empty)
y VARCHAR YES NULL (empty)

query T
SELECT a FROM db2.test4
----
2
3
4

statement error 1
CREATE TABLE db2.test5(a Varchar null, y Varchar null) ENGINE=fuse AS SELECT b FROM db1.test1

statement error 1006
CREATE TABLE db2.test5(a Varchar null, y Varchar null) ENGINE=fuse AS SELECT a, b, a FROM db1.test1

statement ok
create table db2.test55(id Int8, created timestamp  not null DEFAULT CURRENT_TIMESTAMP)

statement ok
insert into db2.test55(id) select number % 3 from numbers(1000)

statement ok
insert into db2.test55(id) select number % 3 from numbers(1000)

query I
select count(distinct created) > 1 from db2.test55;
----
1

statement error 1065
create table db2.test6(id Int8, created timestamp  DEFAULT today() + a)

statement ok
create table db2.test6(id Int8 not null, a Int8 not null DEFAULT 1 + 2, created timestamp not null DEFAULT now())

query TTTTT
desc db2.test6
----
id TINYINT NO 0 (empty)
a TINYINT NO 3 (empty)
created TIMESTAMP NO now() (empty)

statement ok
INSERT INTO db2.test6 (id) VALUES (1)

query IIT
SELECT id, a, now() >= created FROM db2.test6;
----
1 3 1

statement ok
alter table db2.test6 add column b timestamp default now()

statement ok
create table db2.test7(tiny TINYINT(10) not null, tiny_unsigned TINYINT(10) UNSIGNED not null, smallint SMALLINT not null, smallint_unsigned SMALLINT UNSIGNED not null, int INT32(15) not null, int_unsigned UINT32(15) not null, bigint BIGINT not null, bigint_unsigned BIGINT UNSIGNED not null,float FLOAT not null, double DOUBLE not null, date DATE not null, datetime DATETIME not null, ts TIMESTAMP not null, str VARCHAR not null default '3', bool BOOLEAN not null, arr ARRAY(INT) not null, tup TUPLE(INT, BOOL) not null, map MAP(INT, STRING) not null, bitmap BITMAP not null, variant VARIANT not null)

query TTTTT
desc db2.test7
----
tiny TINYINT NO 0 (empty)
tiny_unsigned TINYINT UNSIGNED NO 0 (empty)
smallint SMALLINT NO 0 (empty)
smallint_unsigned SMALLINT UNSIGNED NO 0 (empty)
int INT NO 0 (empty)
int_unsigned INT UNSIGNED NO 0 (empty)
bigint BIGINT NO 0 (empty)
bigint_unsigned BIGINT UNSIGNED NO 0 (empty)
float FLOAT NO 0 (empty)
double DOUBLE NO 0 (empty)
date DATE NO '1970-01-01' (empty)
datetime TIMESTAMP NO '1970-01-01 00:00:00.000000' (empty)
ts TIMESTAMP NO '1970-01-01 00:00:00.000000' (empty)
str VARCHAR NO '3' (empty)
bool BOOLEAN NO false (empty)
arr ARRAY(INT32) NO [] (empty)
tup TUPLE(1 INT32, 2 BOOLEAN) NO (NULL, NULL) (empty)
map MAP(INT32, STRING) NO {} (empty)
bitmap BITMAP NO '' (empty)
variant VARIANT NO 'null' (empty)

statement ok
create transient table db2.test8(tiny TINYINT not null, tiny_unsigned TINYINT UNSIGNED not null, smallint SMALLINT not null, smallint_unsigned SMALLINT UNSIGNED not null, int INT not null, int_unsigned INT UNSIGNED not null, bigint BIGINT not null, bigint_unsigned BIGINT UNSIGNED not null,float FLOAT not null, double DOUBLE not null, date DATE not null, datetime DATETIME not null, ts TIMESTAMP not null, str VARCHAR not null default '3', bool BOOLEAN not null, arr ARRAY(VARCHAR) not null, tup TUPLE(DOUBLE, INT) not null, map MAP(STRING, Date) not null, bitmap BITMAP not null, variant VARIANT not null)

query TTTTT
desc db2.test8
----
tiny TINYINT NO 0 (empty)
tiny_unsigned TINYINT UNSIGNED NO 0 (empty)
smallint SMALLINT NO 0 (empty)
smallint_unsigned SMALLINT UNSIGNED NO 0 (empty)
int INT NO 0 (empty)
int_unsigned INT UNSIGNED NO 0 (empty)
bigint BIGINT NO 0 (empty)
bigint_unsigned BIGINT UNSIGNED NO 0 (empty)
float FLOAT NO 0 (empty)
double DOUBLE NO 0 (empty)
date DATE NO '1970-01-01' (empty)
datetime TIMESTAMP NO '1970-01-01 00:00:00.000000' (empty)
ts TIMESTAMP NO '1970-01-01 00:00:00.000000' (empty)
str VARCHAR NO '3' (empty)
bool BOOLEAN NO false (empty)
arr ARRAY(STRING) NO [] (empty)
tup TUPLE(1 FLOAT64, 2 INT32) NO (NULL, NULL) (empty)
map MAP(STRING, DATE) NO {} (empty)
bitmap BITMAP NO '' (empty)
variant VARIANT NO 'null' (empty)


statement ok
use db2

statement ok
create table test9 like test8

statement ok
create table test10(a int, b tuple(int, int) default (1, 2), c array(int) default [10,20])

query TTTTT
desc test10
----
a INT YES NULL (empty)
b TUPLE(1 INT32, 2 INT32) YES (1, 2) (empty)
c ARRAY(INT32) YES [10, 20] (empty)

statement ok
insert into test10 (a) values (100),(200)

query ITT
select * from test10
----
100 (1,2) [10,20]
200 (1,2) [10,20]

statement ok
use default

statement ok
DROP DATABASE db1

statement ok
DROP DATABASE db2

statement error 1002
CREATE TABLE system.test(a INT)

statement ok
drop table if exists t

statement error Duplicated column name
create table t(a int, a int)

statement error Duplicated column name
create table t(a int, A int)

statement error Duplicated column name
create table t as select number, number from numbers(1)

statement error 4000
create table tb101 (id int ,c1 datetime) 's3://wubx/tb101' connection=(aws_key_id='minioadmin' aws_ssecret_key='minioadmin' endpoint_url='http://127.0.0.1:9900');

statement ok
drop table if exists tt_v2

statement error 3001
create table tt_v2 (id int) engine=fuse SNAPSHOT_LOCATION='xx'

statement error 1301
create table t(a int) x=x

statement error 1301
create table t(a int) external_location='xxx'

statement error 1301
create table t(a int) location='xxx'

statement error 1301
create table t(a int) seed='123'

statement ok
create or replace table t_random(a int) engine=RANDOM seed='123'

statement error 1301
create table t(a int) snapshot_loc='xxx'

statement error 3001
create table t(a int) snapshot_location='xxx'

statement error 1301
create table t(a int) database_id='xxx'

statement error 1006
create table t(a int) bloom_index_columns='b'

statement error 1301
create table t(a decimal(4,2)) bloom_index_columns='a'

statement ok
create table t(a int)

statement error 1301
alter table t set options(database_id = "22");

statement ok
drop table if exists t;

statement ok
drop table if exists t_without_engine_desc;

statement ok
drop table if exists t_with_engine_desc;

statement ok
drop table if exists t_with_wrong_engine_desc;

statement ok
create table t_without_engine_desc(id int);

statement ok
create table t_with_engine_desc(id int) engine=NULL;

statement error 2302
create table t_with_engine_desc(id int);

statement error 2302
create table t_with_engine_desc(id int) engine=NULL;

statement error 1005
create table t_with_wrong_engine_desc(id int) engine=abc;

statement ok
drop table if exists t_without_engine_desc;

statement ok
drop table if exists t_with_engine_desc;

statement ok
drop table if exists t_with_wrong_engine_desc;

statement ok
drop table if exists t_with_bloom_index;

statement ok
create table t_with_bloom_index(a int, b int) bloom_index_columns='b'

statement ok
drop table if exists t_row_per_block;

statement error 1301
create table t_row_per_block(a int) row_per_block = 100000000000;

statement ok
create table t_row_per_block(a int) row_per_block = 10000;

statement error 1301
alter table t_row_per_block set options(row_per_block = 100000000000);

statement ok
alter table t_row_per_block set options(row_per_block = 100000);

statement ok
drop table if exists t_nested

statement ok
create table t_nested(a int, b array(int not null), c array(int null), d map(string, int null), e map(string, string not null), f tuple(int null, string not null, array(int null), array(string not null)))

query TTTTT
desc t_nested
----
a INT YES NULL (empty)
b ARRAY(INT32) YES NULL (empty)
c ARRAY(INT32) YES NULL (empty)
d MAP(STRING, INT32) YES NULL (empty)
e MAP(STRING, STRING) YES NULL (empty)
f TUPLE(1 INT32, 2 STRING, 3 ARRAY(INT32), 4 ARRAY(STRING)) YES NULL (empty)

statement ok
drop table if exists replace_test;

statement ok
create table replace_test(a int);

statement ok
insert into replace_test values(1);

query I
select a from replace_test;
----
1

statement error 1005
create or replace table IF NOT EXISTS replace_test(b int);

statement ok
create or replace table replace_test(b int);

statement error 1065
select a from replace_test;

statement ok
drop table if exists t_default

statement ok
create table t_default(a int, b bitmap not null default '1,2', c variant not null default '{"k":"v"}')

query TTTTT
desc t_default
----
a INT YES NULL (empty)
b BITMAP NO '1,2' (empty)
c VARIANT NO '{"k":"v"}' (empty)

statement ok
insert into t_default (a) values(1),(2)

query ITT
select a, to_string(b), c from t_default
----
1 1,2 {"k":"v"}
2 1,2 {"k":"v"}

statement ok
drop table if exists crt1

statement ok
create or replace table crt1 as select * from numbers(10);

query I
select count(*) from crt1;
----
10

statement ok
create or replace table crt1 as select * from numbers(10);

query I
select count(*) from crt1;
----
10

statement ok
drop table if exists crt1

### Atomic CTAS ###

# CASE: no (visible)table should be created if anything fails
statement error 1006
create table t as select number/0 from numbers(1);

# table t should not be created
# expects:  ERROR 1105 (HY000): UnknownTable. Code: 1025, Text = error: ...
statement error 1025
select * from t;


# CASE: create or replace

# normal case
statement ok
create or replace table t as select * from numbers(1);

query I
select count(*) from t;
----
1

statement ok
create or replace table t as select * from numbers(2);

query I
select count(*) from t;
----
2

statement ok
drop table t;

# replace exists table which created with plain create stmt:
# table t should be replaced, (schema also changed)

statement ok
create table t(c int);

statement ok
insert into table t values(-1);

statement ok
create or replace table t as select * from numbers(2);

query I
select * from t order by number;
----
0
1

statement ok
drop table t;

# create or replace failed:
# table should not be replaced if anything fails

statement ok
create or replace table t as select * from numbers(1);

# table t should not be replaced
statement error 1006
create or replace table t as select number/0 from numbers(2);

query I
select * from t order by number;
----
0

statement ok
drop table t

# CASE: create if not exist

# brand new table

statement ok
create table if not exists t as select * from numbers(100);

query I
select count(*) from t;
----
100


query I
select count(xx.*) from t xx;
----
100


# table exists

statement ok
create table if not exists t as select * from numbers(1);

# table not changed
query I
select count(*) from t;
----
100

statement ok
drop table t

statement error 1081.*is not deterministic
create table t(a int) cluster by (a+rand())

statement error 1081.*is not deterministic
create table t(a string) cluster by (a+uuid())

statement ok
create or replace table tt(a tuple(x int, y int), b string, c int) cluster by (b);

statement ok
insert into tt(b,c) values ('1',2);

##################################################
# table option  `data_retention_period_in_hours` #
##################################################

statement ok
create or replace table t_opt_retention (c int);

# invalid value
# ERROR HY000 (1105): FromStdError. Code: 1001, Text = invalid digit found in string.
statement error
alter table t_opt_retention  set options(data_retention_period_in_hours = 'abc');

# invalid value (larger than default max 90 days, 90 * 24 = 2160)
statement error 1301
alter table t_opt_retention  set options(data_retention_period_in_hours = 3000);

statement ok
alter table t_opt_retention  set options(data_retention_period_in_hours = 2);


#issue 16794
statement ok
set ddl_column_type_nullable=0;

statement ok
create or replace table b as select 123 as col1, null  as col2;

query T
desc b;
----
col1 TINYINT UNSIGNED NO 0 (empty)
col2 VARCHAR YES NULL (empty)

query T
show create table b;
----
b CREATE TABLE b (     col1 TINYINT UNSIGNED NOT NULL,     col2 VARCHAR NULL   ) ENGINE=FUSE

query TT
select * from b;
----
123 NULL

statement ok
drop table if exists b;

statement ok
set ddl_column_type_nullable=1;

statement ok
create or replace table b as select 123 as col1;

query T
desc b;
----
col1 TINYINT UNSIGNED YES NULL (empty)

statement ok
set ddl_column_type_nullable=0;

statement ok
create or replace table b as select 123 as col1;

query T
desc b;
----
col1 TINYINT UNSIGNED NO 0 (empty)

statement ok
unset ddl_column_type_nullable;

statement ok
drop table if exists b;

statement error 1301
create table a (c1 decimal(38), c2 int) partition by (c1, c2) PROPERTIES ("read.split.target-size"='134217728', "read.split.metadata-target-size"='33554432');

statement ok
create or replace table t (a string default '\'0\'', b int);

statement ok
insert into t(b)  values (1)

query
select '\'0\'' = a from t
----
1
