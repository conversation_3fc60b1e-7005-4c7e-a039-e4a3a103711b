// Copyright 2021 Datafuse Labs
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package meta;

import "raft.proto";
import "request.proto";

message Empty {}

message RaftRequest {
  string data = 1;
}

message RaftReply {
  string data = 1;
  string error = 2;
}

message TransferLeaderRequest {
  Vote from = 1;
  uint64 to = 2;
  LogId last_log_id = 3;
}

message MemberListRequest {string data = 1;}

message MemberListReply {repeated string data = 1;}

message HandshakeRequest {
  uint64 protocol_version = 1;
  bytes payload = 2;
}

message HandshakeResponse {
  uint64 protocol_version = 1;
  bytes payload = 2;
}

// Request meta-service to export all data in a stream.
message ExportRequest {
  // The number of lines of json string contained in a stream item.
  // Note that too many lines in a stream item may cause "gRPC message too large" error.
  // The default chunk_size is 32.
  optional uint64 chunk_size = 10;
}

// Data chunk for export/import meta data
message ExportedChunk {
  repeated string data = 10;
}

message WatchRequest {
  // key is the key to register for watching.
  string key = 1;

  // `key_end`` is the end of the range [key, key_end) to watch.
  // If key_end is None, then watch only key.
  // If want to watch prefix of key, use `get_start_and_end_of_prefix` to
  // generate [key, key_end).
  optional string key_end = 2;

  enum FilterType {
    // recv all kind update event.
    ALL = 0;
    // filter only update event.
    UPDATE = 1;
    // filter only delete event.
    DELETE = 2;
  }
  FilterType filter_type = 3;

  // Whether to send current values of all watched keys when watch starts
  // Useful for initializing client-side caches. The client:
  // - first get a full copy of the key-values,
  // - then update every time a key-value is changed.
  bool initial_flush = 4;
}

message Event {
  string key = 1;

  // current hold current value of key(if any)
  optional SeqV current = 2;

  // prev value of key(if any)
  optional SeqV prev = 3;
}

// Response in a watch stream from server to the client.
message WatchResponse {
  // The event containing key-value change information or a value emitted during initial-flush.
  // This includes the key, current value (if any), and previous value (if any).
  Event event = 1;

  // Indicates whether this event is part of the initialization.
  // 
  // When true:
  // - The event represents an existing key-value record at the time the watch was established
  // - These events are sent when initial_flush=true was specified in WatchRequest
  // 
  // When false:
  // - The event represents a real-time change that occurred after the watch was established
  // - A special event with no key-value data may be sent to indicate the completion
  //   of the initial flush phase
  bool is_initialization = 2;
}

// messages for txn
message TxnCondition {
  // condition result
  enum ConditionResult {
    EQ = 0;
    GT = 1;
    GE = 2;
    LT = 3;
    LE = 4;
    NE = 5;
  }

  string key = 1;

  oneof target {
    // Compare the stored value of `key` against the given value.
    bytes value = 2;

    // Compare the stored seq of `key` against the given seq.
    uint64 seq = 3;

    // Compare the count of keys having the prefix `key` against the given value.
    //
    // Usually when using this option, append a slash `/` to the end of the prefix `key`.
    // For example, if you want to count the keys with prefix `foo`, you should use `foo/` as the `key`.
    uint64 keys_with_prefix = 5;
  }

  // the expected result of condition, if `expected` match the condition result,
  // then the `if_then` op will be executed, otherwise `else_then` op will be
  // executed.
  ConditionResult expected = 4;
}

// BooleanExpression represents a tree of transaction conditions combined with logical operators.
// It enables complex condition checking by allowing both simple conditions and nested expressions.
message BooleanExpression {
  // Logical operator to combine multiple conditions, including sub compound conditions or simple conditions.
  enum CombiningOperator {
    AND = 0;
    OR = 1;
  }

  // Operator determining how child expressions and conditions are combined
  CombiningOperator operator = 1;

  // Nested boolean expressions, allowing tree-like structure
  // Example: (A AND B) OR (C AND D) where A,B,C,D are conditions
  repeated BooleanExpression sub_expressions = 2;

  // Leaf-level transaction conditions
  // These are the actual checks performed against the state.
  repeated TxnCondition conditions = 3;
}


message TxnOp {
  oneof request {
    // TODO add Echo to get a response
    TxnGetRequest get = 1;
    TxnPutRequest put = 2;
    TxnDeleteRequest delete = 3;
    TxnDeleteByPrefixRequest delete_by_prefix = 4;
  }
}

message TxnOpResponse {
  oneof response {
    TxnGetResponse get = 1;
    TxnPutResponse put = 2;
    TxnDeleteResponse delete = 3;
    TxnDeleteByPrefixResponse delete_by_prefix = 4;
  }
}

// Represents a set of operations that execute only when specified conditions are met.
message ConditionalOperation {
  // Tree of conditions that must be satisfied for operations to execute
  BooleanExpression predicate = 1;

  // Operations to execute when condition_tree evaluates to true
  // These operations are executed in sequence
  repeated TxnOp operations = 2;
}

// A Transaction request sent to the databend-meta service.
//
// To provide backward compatibility, the `TxnRequest` is processed in the following order:
//
// - Loop and evaluate the condition in the `operations`, and execute the corresponding operation, if the condition is met.
//   This will stop and return once a condition is met and one of the corresponding operations is executed.
//
// - If none of the conditions are met, the `condition` as the **last** condition will be evaluated.
//   And the `if_then` will be executed and return.
//
// - If none operation are executed, `else_then` will be executed.
message TxnRequest {

  // Series of conditional operations to execute.
  // It will stop once a condition is met and one of the corresponding operations is executed
  repeated ConditionalOperation operations = 4;

  // `condition` is a list of predicates.
  // If all of them success, the `if_then` will be executed,
  // otherwise `else_then` op will be executed.
  repeated TxnCondition condition = 1;

  // `if_then` is a list of operations will be executed when all condition
  // evaluates to true.
  repeated TxnOp if_then = 2;

  // `else_then` is a list of operations will be executed when not all condition
  // evaluates to true.
  repeated TxnOp else_then = 3;
}

// TransactionResponse represents the result of executing a transaction.
//
// `execution_path` identifies condition that is executed and is identified as:
// - "operation:{index}": executed operation at index
// - "then": `condition` field were met, and the `if_then` operation is executed.
// - "else": neither operations nor condition are met and `else_then` is executed.
//
// `success` is set to false only when `else_then` is executed.
message TxnReply {
  bool success = 1;

  repeated TxnOpResponse responses = 2;

  // Not used
  // string error = 3;
  reserved 3;

  // Identifies which execution path was taken
  string execution_path = 4;
}

message ClusterStatus {
  uint64 id = 1;
  string binary_version = 2;
  string data_version = 3;
  string endpoint = 4;
  uint64 raft_log_size = 5;
  string state = 6;
  bool is_leader = 7;
  uint64 current_term = 8;
  uint64 last_log_index = 9;
  string last_applied = 10;
  optional string snapshot_last_log_id = 11;
  optional string purged = 12;
  optional string leader = 13;
  map<uint64, string> replication = 14;
  repeated string voters = 15;
  repeated string non_voters = 16;
  uint64 last_seq = 17;
  uint64 snapshot_key_count = 18;
  RaftLogStatus raft_log_status = 19;
}

// Status about local raft-log storage
message RaftLogStatus {
  uint64 cache_items = 1;
  uint64 cache_used_size = 2;
  uint64 wal_total_size = 3;
  uint64 wal_open_chunk_size = 4;
  uint64 wal_offset = 5;
  uint64 wal_closed_chunk_count = 6;
  uint64 wal_closed_chunk_total_size = 7;
  map<string, uint64> wal_closed_chunk_sizes = 8;
}

message ClientInfo {
  // The address of the connected in form of "<ip>:<port>"
  string client_addr = 10;

  // The timestamp when the meta-server received the request in milliseconds since 1970 .
  //
  // It is created with `SeqV::now_ms()`
  // To convert it back to time with:
  // `SystemTime::UNIX_EPOCH + Duration::from_millis(server_time)`
  optional uint64 server_time = 20;
}

// Item for a Streaming read reply, e.g., for `Mget` and `List`.
message StreamItem {
  string key = 1;
  optional SeqV value = 2;
}

message SnapshotChunkRequest {
  uint64 ver = 100;

  // json serialized meta data, including vote and snapshot_meta.
  // ```text
  // (Vote, SnapshotMeta)
  // ```
  string rpc_meta = 10;

  SnapshotChunkV1 chunk = 20;
}

message SnapshotChunkV1 {
  uint64 offset = 10;
  bool done = 11;
  bytes data = 12;
}

// The item of snapshot chunk stream.
//
// The first item contains `rpc_meta`,
// including the application defined format of this snapshot data,
// the leader vote and snapshot-meta.
//
// Since the second item, the `rpc_meta` should be empty and will be ignored by the receiving end.
message SnapshotChunkRequestV003 {

  // json serialized meta data, including vote and snapshot_meta.
  // ```text
  // (SnapshotFormat, Vote, SnapshotMeta)
  // ```
  optional string rpc_meta = 10;

  // Snapshot data chunk
  bytes chunk = 20;
}

// V003 snapshot response
message SnapshotResponseV003 {
  // json serialized remote vote
  string vote = 10;
}

service RaftService {

  // Forward a request to another node.
  rpc Forward(RaftRequest) returns (RaftReply);

  // Handling internally redirected KvReadV1 request.
  // Without checking token.
  rpc KvReadV1(RaftRequest) returns (stream StreamItem);

  // raft RPC

  rpc AppendEntries(RaftRequest) returns (RaftReply);
  rpc InstallSnapshotV1(SnapshotChunkRequest) returns (RaftReply);
  rpc InstallSnapshotV003(stream SnapshotChunkRequestV003) returns (SnapshotResponseV003);
  rpc Vote(RaftRequest) returns (RaftReply);
  rpc TransferLeader(TransferLeaderRequest) returns (Empty);
}

service MetaService {
  // handshake
  rpc Handshake(stream HandshakeRequest) returns (stream HandshakeResponse);

  // General KV API for get, mget, list, and upsert;
  // It is introduced to replace `WriteMsg` and `ReadMsg`.
  //
  // 2022-09-14: since: 0.8.35
  rpc KvApi(RaftRequest) returns (RaftReply);

  // Handle application read request.
  //
  // This API is not exposed to client directly, but is only used for internal request forwarding.
  // The request will be forwarded to leader if current node is not leader.
  // It returns a stream of `StreamItem`.
  // - For single-reply request, the stream contains only one item, e.g. `Get`.
  // - For multi-reply request, the stream contains multiple items, e.g. `MGet` and `List`.
  //
  // 2023-10-17: since 1.2.163
  rpc KvReadV1(RaftRequest) returns (stream StreamItem);

  // Export all meta data.
  //
  // Including raft hard state, logs and state machine.
  // The exported data is a list of json strings in form of `(tree_name,
  // sub_tree_prefix, key, value)`.
  rpc Export(Empty) returns (stream ExportedChunk);

  // Export all meta data.
  //
  // Including raft hard state, logs and state machine.
  // The exported data is a list of json strings in form of:
  // `(tree_name, {"<key_space>":{"key":<key>,"value":<value>}})`.
  //
  // ```text
  // ["raft_log",{"Logs":{"key":83,"value":{"log_id":{"leader_id":{"term":1,"node_id":0},"index":83},"payload":{"Normal":{"txid":null,"time_ms":1667290974891,"cmd":{"UpsertKV":{"key":"__fd_clusters/test_tenant/test_cluster/databend_query/KMZ4VvqDFVExlZFThKDzZ1","seq":{"GE":1},"value":"AsIs","value_meta":{"expire_at":1667291034}}}}}}}}]
  // ["state_machine/0",{"Nodes":{"key":1,"value":{"name":"1","endpoint":{"addr":"localhost","port":28103},"grpc_api_advertise_address":"0.0.0.0:9191"}}}]
  // ```
  rpc ExportV1(ExportRequest) returns (stream ExportedChunk);


  // Add watch key stream.
  // Whenever the watch key data updated, client will be notified across the
  // stream.
  rpc Watch(WatchRequest) returns (stream WatchResponse);

  rpc Transaction(TxnRequest) returns (TxnReply);

  // Get MetaSrv member list endpoints
  rpc MemberList(MemberListRequest) returns (MemberListReply);

  // Get cluster status
  // Since: 2023-10-19
  rpc GetClusterStatus(Empty) returns (ClusterStatus);

  // Respond with the information about the client.
  // Since: 2022-09-09 0.8.30
  rpc GetClientInfo(Empty) returns (ClientInfo);
}
