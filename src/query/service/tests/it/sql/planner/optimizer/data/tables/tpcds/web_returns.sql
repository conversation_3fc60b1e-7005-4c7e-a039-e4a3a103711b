CREATE OR REPLACE TABLE web_returns
(
    wr_returned_date_sk       integer                           null,
    wr_returned_time_sk       integer                           null,
    wr_item_sk                integer                               ,
    wr_refunded_customer_sk   integer                           null,
    wr_refunded_cdemo_sk      integer                           null,
    wr_refunded_hdemo_sk      integer                           null,
    wr_refunded_addr_sk       integer                           null,
    wr_returning_customer_sk  integer                           null,
    wr_returning_cdemo_sk     integer                           null,
    wr_returning_hdemo_sk     integer                           null,
    wr_returning_addr_sk      integer                           null,
    wr_web_page_sk            integer                           null,
    wr_reason_sk              integer                           null,
    wr_order_number           integer                               ,
    wr_return_quantity        integer                           null,
    wr_return_amt             decimal(7,2)                      null,
    wr_return_tax             decimal(7,2)                      null,
    wr_return_amt_inc_tax     decimal(7,2)                      null,
    wr_fee                    decimal(7,2)                      null,
    wr_return_ship_cost       decimal(7,2)                      null,
    wr_refunded_cash          decimal(7,2)                      null,
    wr_reversed_charge        decimal(7,2)                      null,
    wr_account_credit         decimal(7,2)                      null,
    wr_net_loss               decimal(7,2)                      null      
);
