CREATE OR REPLACE TABLE catalog_returns
(
    cr_returned_date_sk       integer                           null,
    cr_returned_time_sk       integer                           null,
    cr_item_sk                integer                               ,
    cr_refunded_customer_sk   integer                           null,
    cr_refunded_cdemo_sk      integer                           null,
    cr_refunded_hdemo_sk      integer                           null,
    cr_refunded_addr_sk       integer                           null,
    cr_returning_customer_sk  integer                           null,
    cr_returning_cdemo_sk     integer                           null,
    cr_returning_hdemo_sk     integer                           null,
    cr_returning_addr_sk      integer                           null,
    cr_call_center_sk         integer                           null,
    cr_catalog_page_sk        integer                           null,
    cr_ship_mode_sk           integer                           null,
    cr_warehouse_sk           integer                           null,
    cr_reason_sk              integer                           null,
    cr_order_number           integer                               ,
    cr_return_quantity        integer                           null,
    cr_return_amount          decimal(7,2)                      null,
    cr_return_tax             decimal(7,2)                      null,
    cr_return_amt_inc_tax     decimal(7,2)                      null,
    cr_fee                    decimal(7,2)                      null,
    cr_return_ship_cost       decimal(7,2)                      null,
    cr_refunded_cash          decimal(7,2)                      null,
    cr_reversed_charge        decimal(7,2)                      null,
    cr_store_credit           decimal(7,2)                      null,
    cr_net_loss               decimal(7,2)                      null       
);
