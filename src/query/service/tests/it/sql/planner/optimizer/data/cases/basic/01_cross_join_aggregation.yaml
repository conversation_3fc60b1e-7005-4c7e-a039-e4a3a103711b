name: "01_cross_join_aggregation"
description: "Test for cross join with aggregation functions"

sql: |
  SELECT SUM(i1.i),  MIN(i1.i), MAX(i2.i) FROM integers i1, integers i2;

auto_statistics: true

good_plan: |
  Result [output: DT.D_YEAR, ITEM.I_BRAND_ID, ITEM.I_BRAND, SUM(...)]
  └── SortWithLimit [limit: 100]
      ├── sort keys: [DT.D_YEAR ASC NULLS LAST, SUM(SS_EXT_SALES_PRICE) DESC NULLS FIRST, ITEM.I_BRAND_ID ASC NULLS LAST]
      └── Aggregate [group by: DT.D_YEAR, ITEM.I_BRAND, ITEM.I_BRAND_ID]
          └── Aggregate [group by: DT.D_YEAR, ITEM.I_BRAND, ITEM.I_BRAND_ID]
              └── InnerJoin [join key: (DT.D_DATE_SK = STORE_SALES.SS_SOLD_DATE_SK)]
                  ├── Filter [condition: DT.D_MOY = 11]
                  │   └── TableScan (DATE_DIM as DT) [partitions: 1/1, bytes: 2,138,624]
                  │       └── columns: [D_DATE_SK, D_YEAR, D_MOY]
                  └── Aggregate [group by: ITEM.I_BRAND_ID, ITEM.I_BRAND, STORE_SALES.SS_SOLD_DATE_SK]
                      └── InnerJoin [join key: (ITEM.I_ITEM_SK = STORE_SALES.SS_ITEM_SK)]
