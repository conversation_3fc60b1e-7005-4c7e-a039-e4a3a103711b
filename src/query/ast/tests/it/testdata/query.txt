---------- Input ----------
select ?
---------- Output ---------
SELECT ?
---------- AST ------------
Query {
    span: Some(
        0..8,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..8,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                AliasedExpr {
                    expr: Placeholder {
                        span: Some(
                            7..8,
                        ),
                    },
                    alias: None,
                },
            ],
            from: [],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * exclude c1, b.* exclude (c2, c3, c4) from customer inner join orders on a = b limit 1
---------- Output ---------
SELECT * EXCLUDE (c1), b.* EXCLUDE (c2, c3, c4) FROM customer INNER JOIN orders ON a = b LIMIT 1
---------- AST ------------
Query {
    span: Some(
        0..86,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..86,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: Some(
                        Excludes(
                            [
                                Identifier {
                                    span: Some(
                                        17..19,
                                    ),
                                    name: "c1",
                                    quote: None,
                                    ident_type: None,
                                },
                            ],
                        ),
                    ),
                },
                StarColumns {
                    qualified: [
                        Identifier(
                            Identifier {
                                span: Some(
                                    21..22,
                                ),
                                name: "b",
                                quote: None,
                                ident_type: None,
                            },
                        ),
                        Star(
                            Some(
                                23..24,
                            ),
                        ),
                    ],
                    column_filter: Some(
                        Excludes(
                            [
                                Identifier {
                                    span: Some(
                                        34..36,
                                    ),
                                    name: "c2",
                                    quote: None,
                                    ident_type: None,
                                },
                                Identifier {
                                    span: Some(
                                        38..40,
                                    ),
                                    name: "c3",
                                    quote: None,
                                    ident_type: None,
                                },
                                Identifier {
                                    span: Some(
                                        42..44,
                                    ),
                                    name: "c4",
                                    quote: None,
                                    ident_type: None,
                                },
                            ],
                        ),
                    ),
                },
            ],
            from: [
                Join {
                    span: Some(
                        60..70,
                    ),
                    join: Join {
                        op: Inner,
                        condition: On(
                            BinaryOp {
                                span: Some(
                                    83..84,
                                ),
                                op: Eq,
                                left: ColumnRef {
                                    span: Some(
                                        81..82,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    81..82,
                                                ),
                                                name: "a",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                                right: ColumnRef {
                                    span: Some(
                                        85..86,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    85..86,
                                                ),
                                                name: "b",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                            },
                        ),
                        left: Table {
                            span: Some(
                                51..59,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    51..59,
                                ),
                                name: "customer",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                        right: Table {
                            span: Some(
                                71..77,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    71..77,
                                ),
                                name: "orders",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    },
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [
        Literal {
            span: Some(
                93..94,
            ),
            value: UInt64(
                1,
            ),
        },
    ],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select columns('abc'), columns(a -> length(a) = 3) from t
---------- Output ---------
SELECT COLUMNS(_t -> _t REGEXP 'abc'), COLUMNS(a -> length(a) = 3) FROM t
---------- AST ------------
Query {
    span: Some(
        0..57,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..57,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..14,
                            ),
                        ),
                    ],
                    column_filter: Some(
                        Lambda(
                            Lambda {
                                params: [
                                    Identifier {
                                        span: Some(
                                            7..14,
                                        ),
                                        name: "_t",
                                        quote: None,
                                        ident_type: None,
                                    },
                                ],
                                expr: BinaryOp {
                                    span: Some(
                                        7..14,
                                    ),
                                    op: Regexp,
                                    left: ColumnRef {
                                        span: None,
                                        column: ColumnRef {
                                            database: None,
                                            table: None,
                                            column: Name(
                                                Identifier {
                                                    span: Some(
                                                        7..14,
                                                    ),
                                                    name: "_t",
                                                    quote: None,
                                                    ident_type: None,
                                                },
                                            ),
                                        },
                                    },
                                    right: Literal {
                                        span: Some(
                                            7..14,
                                        ),
                                        value: String(
                                            "abc",
                                        ),
                                    },
                                },
                            },
                        ),
                    ),
                },
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                23..30,
                            ),
                        ),
                    ],
                    column_filter: Some(
                        Lambda(
                            Lambda {
                                params: [
                                    Identifier {
                                        span: Some(
                                            31..32,
                                        ),
                                        name: "a",
                                        quote: None,
                                        ident_type: None,
                                    },
                                ],
                                expr: BinaryOp {
                                    span: Some(
                                        46..47,
                                    ),
                                    op: Eq,
                                    left: FunctionCall {
                                        span: Some(
                                            36..45,
                                        ),
                                        func: FunctionCall {
                                            distinct: false,
                                            name: Identifier {
                                                span: Some(
                                                    36..42,
                                                ),
                                                name: "length",
                                                quote: None,
                                                ident_type: None,
                                            },
                                            args: [
                                                ColumnRef {
                                                    span: Some(
                                                        43..44,
                                                    ),
                                                    column: ColumnRef {
                                                        database: None,
                                                        table: None,
                                                        column: Name(
                                                            Identifier {
                                                                span: Some(
                                                                    43..44,
                                                                ),
                                                                name: "a",
                                                                quote: None,
                                                                ident_type: None,
                                                            },
                                                        ),
                                                    },
                                                },
                                            ],
                                            params: [],
                                            order_by: [],
                                            window: None,
                                            lambda: None,
                                        },
                                    },
                                    right: Literal {
                                        span: Some(
                                            48..49,
                                        ),
                                        value: UInt64(
                                            3,
                                        ),
                                    },
                                },
                            },
                        ),
                    ),
                },
            ],
            from: [
                Table {
                    span: Some(
                        56..57,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            56..57,
                        ),
                        name: "t",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: None,
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select count(t.*) from t
---------- Output ---------
SELECT COUNT(t.*) FROM t
---------- AST ------------
Query {
    span: Some(
        0..24,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..24,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                AliasedExpr {
                    expr: CountAll {
                        span: Some(
                            7..17,
                        ),
                        qualified: [
                            Identifier(
                                Identifier {
                                    span: Some(
                                        13..14,
                                    ),
                                    name: "t",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                            Star(
                                Some(
                                    15..16,
                                ),
                            ),
                        ],
                        window: None,
                    },
                    alias: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        23..24,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            23..24,
                        ),
                        name: "t",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: None,
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from customer at(offset => -10 * 30)
---------- Output ---------
SELECT * FROM customer AT (OFFSET => - 10 * 30)
---------- AST ------------
Query {
    span: Some(
        0..45,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..45,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        14..45,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            14..22,
                        ),
                        name: "customer",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: Some(
                        TimeTravel(
                            Offset(
                                BinaryOp {
                                    span: Some(
                                        40..41,
                                    ),
                                    op: Multiply,
                                    left: UnaryOp {
                                        span: Some(
                                            36..37,
                                        ),
                                        op: Minus,
                                        expr: Literal {
                                            span: Some(
                                                37..39,
                                            ),
                                            value: UInt64(
                                                10,
                                            ),
                                        },
                                    },
                                    right: Literal {
                                        span: Some(
                                            42..44,
                                        ),
                                        value: UInt64(
                                            30,
                                        ),
                                    },
                                },
                            ),
                        ),
                    ),
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from customer changes(information => default) at (stream => s) order by a, b
---------- Output ---------
SELECT * FROM customer CHANGES (INFORMATION => DEFAULT) AT (STREAM => s) ORDER BY a, b
---------- AST ------------
Query {
    span: Some(
        0..71,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..71,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        14..71,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            14..22,
                        ),
                        name: "customer",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: Some(
                        Changes(
                            ChangesInterval {
                                append_only: false,
                                at_point: Stream {
                                    catalog: None,
                                    database: None,
                                    name: Identifier {
                                        span: Some(
                                            69..70,
                                        ),
                                        name: "s",
                                        quote: None,
                                        ident_type: None,
                                    },
                                },
                                end_point: None,
                            },
                        ),
                    ),
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [
        OrderByExpr {
            expr: ColumnRef {
                span: Some(
                    81..82,
                ),
                column: ColumnRef {
                    database: None,
                    table: None,
                    column: Name(
                        Identifier {
                            span: Some(
                                81..82,
                            ),
                            name: "a",
                            quote: None,
                            ident_type: None,
                        },
                    ),
                },
            },
            asc: None,
            nulls_first: None,
        },
        OrderByExpr {
            expr: ColumnRef {
                span: Some(
                    84..85,
                ),
                column: ColumnRef {
                    database: None,
                    table: None,
                    column: Name(
                        Identifier {
                            span: Some(
                                84..85,
                            ),
                            name: "b",
                            quote: None,
                            ident_type: None,
                        },
                    ),
                },
            },
            asc: None,
            nulls_first: None,
        },
    ],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from customer with consume as s
---------- Output ---------
SELECT * FROM customer WITH (consume = 'true') AS s
---------- AST ------------
Query {
    span: Some(
        0..40,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..40,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        14..40,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            14..22,
                        ),
                        name: "customer",
                        quote: None,
                        ident_type: None,
                    },
                    alias: Some(
                        TableAlias {
                            name: Identifier {
                                span: Some(
                                    39..40,
                                ),
                                name: "s",
                                quote: None,
                                ident_type: None,
                            },
                            columns: [],
                        },
                    ),
                    temporal: None,
                    with_options: Some(
                        WithOptions {
                            options: {
                                "consume": "true",
                            },
                        },
                    ),
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from t12_0004 at (TIMESTAMP => 'xxxx') as t
---------- Output ---------
SELECT * FROM t12_0004 AT (TIMESTAMP => 'xxxx') AS t
---------- AST ------------
Query {
    span: Some(
        0..52,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..52,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        14..52,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            14..22,
                        ),
                        name: "t12_0004",
                        quote: None,
                        ident_type: None,
                    },
                    alias: Some(
                        TableAlias {
                            name: Identifier {
                                span: Some(
                                    51..52,
                                ),
                                name: "t",
                                quote: None,
                                ident_type: None,
                            },
                            columns: [],
                        },
                    ),
                    temporal: Some(
                        TimeTravel(
                            Timestamp(
                                Literal {
                                    span: Some(
                                        40..46,
                                    ),
                                    value: String(
                                        "xxxx",
                                    ),
                                },
                            ),
                        ),
                    ),
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select count(t.c) from t12_0004 at (snapshot => 'xxxx') as t
---------- Output ---------
SELECT count(t.c) FROM t12_0004 AT (SNAPSHOT => 'xxxx') AS t
---------- AST ------------
Query {
    span: Some(
        0..60,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..60,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                AliasedExpr {
                    expr: FunctionCall {
                        span: Some(
                            7..17,
                        ),
                        func: FunctionCall {
                            distinct: false,
                            name: Identifier {
                                span: Some(
                                    7..12,
                                ),
                                name: "count",
                                quote: None,
                                ident_type: None,
                            },
                            args: [
                                ColumnRef {
                                    span: Some(
                                        13..14,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: Some(
                                            Identifier {
                                                span: Some(
                                                    13..14,
                                                ),
                                                name: "t",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    15..16,
                                                ),
                                                name: "c",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                            ],
                            params: [],
                            order_by: [],
                            window: None,
                            lambda: None,
                        },
                    },
                    alias: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        23..60,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            23..31,
                        ),
                        name: "t12_0004",
                        quote: None,
                        ident_type: None,
                    },
                    alias: Some(
                        TableAlias {
                            name: Identifier {
                                span: Some(
                                    59..60,
                                ),
                                name: "t",
                                quote: None,
                                ident_type: None,
                            },
                            columns: [],
                        },
                    ),
                    temporal: Some(
                        TimeTravel(
                            Snapshot(
                                "xxxx",
                            ),
                        ),
                    ),
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from customer inner join orders
---------- Output ---------
SELECT * FROM customer INNER JOIN orders
---------- AST ------------
Query {
    span: Some(
        0..40,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..40,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Join {
                    span: Some(
                        23..33,
                    ),
                    join: Join {
                        op: Inner,
                        condition: None,
                        left: Table {
                            span: Some(
                                14..22,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    14..22,
                                ),
                                name: "customer",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                        right: Table {
                            span: Some(
                                34..40,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    34..40,
                                ),
                                name: "orders",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    },
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from customer cross join orders
---------- Output ---------
SELECT * FROM customer CROSS JOIN orders
---------- AST ------------
Query {
    span: Some(
        0..40,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..40,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Join {
                    span: Some(
                        23..33,
                    ),
                    join: Join {
                        op: CrossJoin,
                        condition: None,
                        left: Table {
                            span: Some(
                                14..22,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    14..22,
                                ),
                                name: "customer",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                        right: Table {
                            span: Some(
                                34..40,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    34..40,
                                ),
                                name: "orders",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    },
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from customer inner join orders on (a = b)
---------- Output ---------
SELECT * FROM customer INNER JOIN orders ON a = b
---------- AST ------------
Query {
    span: Some(
        0..51,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..51,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Join {
                    span: Some(
                        23..33,
                    ),
                    join: Join {
                        op: Inner,
                        condition: On(
                            BinaryOp {
                                span: Some(
                                    47..48,
                                ),
                                op: Eq,
                                left: ColumnRef {
                                    span: Some(
                                        45..46,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    45..46,
                                                ),
                                                name: "a",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                                right: ColumnRef {
                                    span: Some(
                                        49..50,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    49..50,
                                                ),
                                                name: "b",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                            },
                        ),
                        left: Table {
                            span: Some(
                                14..22,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    14..22,
                                ),
                                name: "customer",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                        right: Table {
                            span: Some(
                                34..40,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    34..40,
                                ),
                                name: "orders",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    },
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from customer inner join orders on a = b limit 1
---------- Output ---------
SELECT * FROM customer INNER JOIN orders ON a = b LIMIT 1
---------- AST ------------
Query {
    span: Some(
        0..49,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..49,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Join {
                    span: Some(
                        23..33,
                    ),
                    join: Join {
                        op: Inner,
                        condition: On(
                            BinaryOp {
                                span: Some(
                                    46..47,
                                ),
                                op: Eq,
                                left: ColumnRef {
                                    span: Some(
                                        44..45,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    44..45,
                                                ),
                                                name: "a",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                                right: ColumnRef {
                                    span: Some(
                                        48..49,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    48..49,
                                                ),
                                                name: "b",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                            },
                        ),
                        left: Table {
                            span: Some(
                                14..22,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    14..22,
                                ),
                                name: "customer",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                        right: Table {
                            span: Some(
                                34..40,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    34..40,
                                ),
                                name: "orders",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    },
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [
        Literal {
            span: Some(
                56..57,
            ),
            value: UInt64(
                1,
            ),
        },
    ],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from customer inner join orders on a = b limit 2 offset 3
---------- Output ---------
SELECT * FROM customer INNER JOIN orders ON a = b LIMIT 2 OFFSET 3
---------- AST ------------
Query {
    span: Some(
        0..49,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..49,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Join {
                    span: Some(
                        23..33,
                    ),
                    join: Join {
                        op: Inner,
                        condition: On(
                            BinaryOp {
                                span: Some(
                                    46..47,
                                ),
                                op: Eq,
                                left: ColumnRef {
                                    span: Some(
                                        44..45,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    44..45,
                                                ),
                                                name: "a",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                                right: ColumnRef {
                                    span: Some(
                                        48..49,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    48..49,
                                                ),
                                                name: "b",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                            },
                        ),
                        left: Table {
                            span: Some(
                                14..22,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    14..22,
                                ),
                                name: "customer",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                        right: Table {
                            span: Some(
                                34..40,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    34..40,
                                ),
                                name: "orders",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    },
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [
        Literal {
            span: Some(
                56..57,
            ),
            value: UInt64(
                2,
            ),
        },
    ],
    offset: Some(
        Literal {
            span: Some(
                65..66,
            ),
            value: UInt64(
                3,
            ),
        },
    ),
    ignore_result: false,
}


---------- Input ----------
select * from customer natural full join orders
---------- Output ---------
SELECT * FROM customer NATURAL FULL OUTER JOIN orders
---------- AST ------------
Query {
    span: Some(
        0..47,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..47,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Join {
                    span: Some(
                        23..40,
                    ),
                    join: Join {
                        op: FullOuter,
                        condition: Natural,
                        left: Table {
                            span: Some(
                                14..22,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    14..22,
                                ),
                                name: "customer",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                        right: Table {
                            span: Some(
                                41..47,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    41..47,
                                ),
                                name: "orders",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    },
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from customer natural join orders left outer join detail using (id)
---------- Output ---------
SELECT * FROM customer NATURAL INNER JOIN orders LEFT OUTER JOIN detail USING(id)
---------- AST ------------
Query {
    span: Some(
        0..76,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..76,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Join {
                    span: Some(
                        43..58,
                    ),
                    join: Join {
                        op: LeftOuter,
                        condition: Using(
                            [
                                Identifier {
                                    span: Some(
                                        73..75,
                                    ),
                                    name: "id",
                                    quote: None,
                                    ident_type: None,
                                },
                            ],
                        ),
                        left: Join {
                            span: Some(
                                23..35,
                            ),
                            join: Join {
                                op: Inner,
                                condition: Natural,
                                left: Table {
                                    span: Some(
                                        14..22,
                                    ),
                                    catalog: None,
                                    database: None,
                                    table: Identifier {
                                        span: Some(
                                            14..22,
                                        ),
                                        name: "customer",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                    temporal: None,
                                    with_options: None,
                                    pivot: None,
                                    unpivot: None,
                                    sample: None,
                                },
                                right: Table {
                                    span: Some(
                                        36..42,
                                    ),
                                    catalog: None,
                                    database: None,
                                    table: Identifier {
                                        span: Some(
                                            36..42,
                                        ),
                                        name: "orders",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                    temporal: None,
                                    with_options: None,
                                    pivot: None,
                                    unpivot: None,
                                    sample: None,
                                },
                            },
                        },
                        right: Table {
                            span: Some(
                                59..65,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    59..65,
                                ),
                                name: "detail",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    },
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
with t2(tt) as (select a from t) select t2.tt from t2  where t2.tt > 1
---------- Output ---------
WITH t2(tt) AS (SELECT a FROM t) SELECT t2.tt FROM t2 WHERE t2.tt > 1
---------- AST ------------
Query {
    span: Some(
        33..70,
    ),
    with: Some(
        With {
            span: Some(
                0..32,
            ),
            recursive: false,
            ctes: [
                CTE {
                    span: Some(
                        5..32,
                    ),
                    alias: TableAlias {
                        name: Identifier {
                            span: Some(
                                5..7,
                            ),
                            name: "t2",
                            quote: None,
                            ident_type: None,
                        },
                        columns: [
                            Identifier {
                                span: Some(
                                    8..10,
                                ),
                                name: "tt",
                                quote: None,
                                ident_type: None,
                            },
                        ],
                    },
                    materialized: false,
                    query: Query {
                        span: Some(
                            16..31,
                        ),
                        with: None,
                        body: Select(
                            SelectStmt {
                                span: Some(
                                    16..31,
                                ),
                                hints: None,
                                distinct: false,
                                top_n: None,
                                select_list: [
                                    AliasedExpr {
                                        expr: ColumnRef {
                                            span: Some(
                                                23..24,
                                            ),
                                            column: ColumnRef {
                                                database: None,
                                                table: None,
                                                column: Name(
                                                    Identifier {
                                                        span: Some(
                                                            23..24,
                                                        ),
                                                        name: "a",
                                                        quote: None,
                                                        ident_type: None,
                                                    },
                                                ),
                                            },
                                        },
                                        alias: None,
                                    },
                                ],
                                from: [
                                    Table {
                                        span: Some(
                                            30..31,
                                        ),
                                        catalog: None,
                                        database: None,
                                        table: Identifier {
                                            span: Some(
                                                30..31,
                                            ),
                                            name: "t",
                                            quote: None,
                                            ident_type: None,
                                        },
                                        alias: None,
                                        temporal: None,
                                        with_options: None,
                                        pivot: None,
                                        unpivot: None,
                                        sample: None,
                                    },
                                ],
                                selection: None,
                                group_by: None,
                                having: None,
                                window_list: None,
                                qualify: None,
                            },
                        ),
                        order_by: [],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                },
            ],
        },
    ),
    body: Select(
        SelectStmt {
            span: Some(
                33..70,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                AliasedExpr {
                    expr: ColumnRef {
                        span: Some(
                            40..42,
                        ),
                        column: ColumnRef {
                            database: None,
                            table: Some(
                                Identifier {
                                    span: Some(
                                        40..42,
                                    ),
                                    name: "t2",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                            column: Name(
                                Identifier {
                                    span: Some(
                                        43..45,
                                    ),
                                    name: "tt",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                        },
                    },
                    alias: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        51..53,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            51..53,
                        ),
                        name: "t2",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: None,
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
            ],
            selection: Some(
                BinaryOp {
                    span: Some(
                        67..68,
                    ),
                    op: Gt,
                    left: ColumnRef {
                        span: Some(
                            61..63,
                        ),
                        column: ColumnRef {
                            database: None,
                            table: Some(
                                Identifier {
                                    span: Some(
                                        61..63,
                                    ),
                                    name: "t2",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                            column: Name(
                                Identifier {
                                    span: Some(
                                        64..66,
                                    ),
                                    name: "tt",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                        },
                    },
                    right: Literal {
                        span: Some(
                            69..70,
                        ),
                        value: UInt64(
                            1,
                        ),
                    },
                },
            ),
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
with t2(tt) as materialized (select a from t) select t2.tt from t2  where t2.tt > 1
---------- Output ---------
WITH t2(tt) AS MATERIALIZED (SELECT a FROM t) SELECT t2.tt FROM t2 WHERE t2.tt > 1
---------- AST ------------
Query {
    span: Some(
        46..83,
    ),
    with: Some(
        With {
            span: Some(
                0..45,
            ),
            recursive: false,
            ctes: [
                CTE {
                    span: Some(
                        5..45,
                    ),
                    alias: TableAlias {
                        name: Identifier {
                            span: Some(
                                5..7,
                            ),
                            name: "t2",
                            quote: None,
                            ident_type: None,
                        },
                        columns: [
                            Identifier {
                                span: Some(
                                    8..10,
                                ),
                                name: "tt",
                                quote: None,
                                ident_type: None,
                            },
                        ],
                    },
                    materialized: true,
                    query: Query {
                        span: Some(
                            29..44,
                        ),
                        with: None,
                        body: Select(
                            SelectStmt {
                                span: Some(
                                    29..44,
                                ),
                                hints: None,
                                distinct: false,
                                top_n: None,
                                select_list: [
                                    AliasedExpr {
                                        expr: ColumnRef {
                                            span: Some(
                                                36..37,
                                            ),
                                            column: ColumnRef {
                                                database: None,
                                                table: None,
                                                column: Name(
                                                    Identifier {
                                                        span: Some(
                                                            36..37,
                                                        ),
                                                        name: "a",
                                                        quote: None,
                                                        ident_type: None,
                                                    },
                                                ),
                                            },
                                        },
                                        alias: None,
                                    },
                                ],
                                from: [
                                    Table {
                                        span: Some(
                                            43..44,
                                        ),
                                        catalog: None,
                                        database: None,
                                        table: Identifier {
                                            span: Some(
                                                43..44,
                                            ),
                                            name: "t",
                                            quote: None,
                                            ident_type: None,
                                        },
                                        alias: None,
                                        temporal: None,
                                        with_options: None,
                                        pivot: None,
                                        unpivot: None,
                                        sample: None,
                                    },
                                ],
                                selection: None,
                                group_by: None,
                                having: None,
                                window_list: None,
                                qualify: None,
                            },
                        ),
                        order_by: [],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                },
            ],
        },
    ),
    body: Select(
        SelectStmt {
            span: Some(
                46..83,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                AliasedExpr {
                    expr: ColumnRef {
                        span: Some(
                            53..55,
                        ),
                        column: ColumnRef {
                            database: None,
                            table: Some(
                                Identifier {
                                    span: Some(
                                        53..55,
                                    ),
                                    name: "t2",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                            column: Name(
                                Identifier {
                                    span: Some(
                                        56..58,
                                    ),
                                    name: "tt",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                        },
                    },
                    alias: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        64..66,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            64..66,
                        ),
                        name: "t2",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: None,
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
            ],
            selection: Some(
                BinaryOp {
                    span: Some(
                        80..81,
                    ),
                    op: Gt,
                    left: ColumnRef {
                        span: Some(
                            74..76,
                        ),
                        column: ColumnRef {
                            database: None,
                            table: Some(
                                Identifier {
                                    span: Some(
                                        74..76,
                                    ),
                                    name: "t2",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                            column: Name(
                                Identifier {
                                    span: Some(
                                        77..79,
                                    ),
                                    name: "tt",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                        },
                    },
                    right: Literal {
                        span: Some(
                            82..83,
                        ),
                        value: UInt64(
                            1,
                        ),
                    },
                },
            ),
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
with t2 as (select a from t) select t2.a from t2  where t2.a > 1
---------- Output ---------
WITH t2 AS (SELECT a FROM t) SELECT t2.a FROM t2 WHERE t2.a > 1
---------- AST ------------
Query {
    span: Some(
        29..64,
    ),
    with: Some(
        With {
            span: Some(
                0..28,
            ),
            recursive: false,
            ctes: [
                CTE {
                    span: Some(
                        5..28,
                    ),
                    alias: TableAlias {
                        name: Identifier {
                            span: Some(
                                5..7,
                            ),
                            name: "t2",
                            quote: None,
                            ident_type: None,
                        },
                        columns: [],
                    },
                    materialized: false,
                    query: Query {
                        span: Some(
                            12..27,
                        ),
                        with: None,
                        body: Select(
                            SelectStmt {
                                span: Some(
                                    12..27,
                                ),
                                hints: None,
                                distinct: false,
                                top_n: None,
                                select_list: [
                                    AliasedExpr {
                                        expr: ColumnRef {
                                            span: Some(
                                                19..20,
                                            ),
                                            column: ColumnRef {
                                                database: None,
                                                table: None,
                                                column: Name(
                                                    Identifier {
                                                        span: Some(
                                                            19..20,
                                                        ),
                                                        name: "a",
                                                        quote: None,
                                                        ident_type: None,
                                                    },
                                                ),
                                            },
                                        },
                                        alias: None,
                                    },
                                ],
                                from: [
                                    Table {
                                        span: Some(
                                            26..27,
                                        ),
                                        catalog: None,
                                        database: None,
                                        table: Identifier {
                                            span: Some(
                                                26..27,
                                            ),
                                            name: "t",
                                            quote: None,
                                            ident_type: None,
                                        },
                                        alias: None,
                                        temporal: None,
                                        with_options: None,
                                        pivot: None,
                                        unpivot: None,
                                        sample: None,
                                    },
                                ],
                                selection: None,
                                group_by: None,
                                having: None,
                                window_list: None,
                                qualify: None,
                            },
                        ),
                        order_by: [],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                },
            ],
        },
    ),
    body: Select(
        SelectStmt {
            span: Some(
                29..64,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                AliasedExpr {
                    expr: ColumnRef {
                        span: Some(
                            36..38,
                        ),
                        column: ColumnRef {
                            database: None,
                            table: Some(
                                Identifier {
                                    span: Some(
                                        36..38,
                                    ),
                                    name: "t2",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                            column: Name(
                                Identifier {
                                    span: Some(
                                        39..40,
                                    ),
                                    name: "a",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                        },
                    },
                    alias: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        46..48,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            46..48,
                        ),
                        name: "t2",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: None,
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
            ],
            selection: Some(
                BinaryOp {
                    span: Some(
                        61..62,
                    ),
                    op: Gt,
                    left: ColumnRef {
                        span: Some(
                            56..58,
                        ),
                        column: ColumnRef {
                            database: None,
                            table: Some(
                                Identifier {
                                    span: Some(
                                        56..58,
                                    ),
                                    name: "t2",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                            column: Name(
                                Identifier {
                                    span: Some(
                                        59..60,
                                    ),
                                    name: "a",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                        },
                    },
                    right: Literal {
                        span: Some(
                            63..64,
                        ),
                        value: UInt64(
                            1,
                        ),
                    },
                },
            ),
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
with t2(tt) as materialized (select a from t), t3 as materialized (select * from t), t4 as (select a from t where a > 1) select t2.tt, t3.a, t4.a from t2, t3, t4 where t2.tt > 1
---------- Output ---------
WITH t2(tt) AS MATERIALIZED (SELECT a FROM t), t3 AS MATERIALIZED (SELECT * FROM t), t4 AS (SELECT a FROM t WHERE a > 1) SELECT t2.tt, t3.a, t4.a FROM t2, t3, t4 WHERE t2.tt > 1
---------- AST ------------
Query {
    span: Some(
        121..177,
    ),
    with: Some(
        With {
            span: Some(
                0..120,
            ),
            recursive: false,
            ctes: [
                CTE {
                    span: Some(
                        5..45,
                    ),
                    alias: TableAlias {
                        name: Identifier {
                            span: Some(
                                5..7,
                            ),
                            name: "t2",
                            quote: None,
                            ident_type: None,
                        },
                        columns: [
                            Identifier {
                                span: Some(
                                    8..10,
                                ),
                                name: "tt",
                                quote: None,
                                ident_type: None,
                            },
                        ],
                    },
                    materialized: true,
                    query: Query {
                        span: Some(
                            29..44,
                        ),
                        with: None,
                        body: Select(
                            SelectStmt {
                                span: Some(
                                    29..44,
                                ),
                                hints: None,
                                distinct: false,
                                top_n: None,
                                select_list: [
                                    AliasedExpr {
                                        expr: ColumnRef {
                                            span: Some(
                                                36..37,
                                            ),
                                            column: ColumnRef {
                                                database: None,
                                                table: None,
                                                column: Name(
                                                    Identifier {
                                                        span: Some(
                                                            36..37,
                                                        ),
                                                        name: "a",
                                                        quote: None,
                                                        ident_type: None,
                                                    },
                                                ),
                                            },
                                        },
                                        alias: None,
                                    },
                                ],
                                from: [
                                    Table {
                                        span: Some(
                                            43..44,
                                        ),
                                        catalog: None,
                                        database: None,
                                        table: Identifier {
                                            span: Some(
                                                43..44,
                                            ),
                                            name: "t",
                                            quote: None,
                                            ident_type: None,
                                        },
                                        alias: None,
                                        temporal: None,
                                        with_options: None,
                                        pivot: None,
                                        unpivot: None,
                                        sample: None,
                                    },
                                ],
                                selection: None,
                                group_by: None,
                                having: None,
                                window_list: None,
                                qualify: None,
                            },
                        ),
                        order_by: [],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                },
                CTE {
                    span: Some(
                        47..83,
                    ),
                    alias: TableAlias {
                        name: Identifier {
                            span: Some(
                                47..49,
                            ),
                            name: "t3",
                            quote: None,
                            ident_type: None,
                        },
                        columns: [],
                    },
                    materialized: true,
                    query: Query {
                        span: Some(
                            67..82,
                        ),
                        with: None,
                        body: Select(
                            SelectStmt {
                                span: Some(
                                    67..82,
                                ),
                                hints: None,
                                distinct: false,
                                top_n: None,
                                select_list: [
                                    StarColumns {
                                        qualified: [
                                            Star(
                                                Some(
                                                    74..75,
                                                ),
                                            ),
                                        ],
                                        column_filter: None,
                                    },
                                ],
                                from: [
                                    Table {
                                        span: Some(
                                            81..82,
                                        ),
                                        catalog: None,
                                        database: None,
                                        table: Identifier {
                                            span: Some(
                                                81..82,
                                            ),
                                            name: "t",
                                            quote: None,
                                            ident_type: None,
                                        },
                                        alias: None,
                                        temporal: None,
                                        with_options: None,
                                        pivot: None,
                                        unpivot: None,
                                        sample: None,
                                    },
                                ],
                                selection: None,
                                group_by: None,
                                having: None,
                                window_list: None,
                                qualify: None,
                            },
                        ),
                        order_by: [],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                },
                CTE {
                    span: Some(
                        85..120,
                    ),
                    alias: TableAlias {
                        name: Identifier {
                            span: Some(
                                85..87,
                            ),
                            name: "t4",
                            quote: None,
                            ident_type: None,
                        },
                        columns: [],
                    },
                    materialized: false,
                    query: Query {
                        span: Some(
                            92..119,
                        ),
                        with: None,
                        body: Select(
                            SelectStmt {
                                span: Some(
                                    92..119,
                                ),
                                hints: None,
                                distinct: false,
                                top_n: None,
                                select_list: [
                                    AliasedExpr {
                                        expr: ColumnRef {
                                            span: Some(
                                                99..100,
                                            ),
                                            column: ColumnRef {
                                                database: None,
                                                table: None,
                                                column: Name(
                                                    Identifier {
                                                        span: Some(
                                                            99..100,
                                                        ),
                                                        name: "a",
                                                        quote: None,
                                                        ident_type: None,
                                                    },
                                                ),
                                            },
                                        },
                                        alias: None,
                                    },
                                ],
                                from: [
                                    Table {
                                        span: Some(
                                            106..107,
                                        ),
                                        catalog: None,
                                        database: None,
                                        table: Identifier {
                                            span: Some(
                                                106..107,
                                            ),
                                            name: "t",
                                            quote: None,
                                            ident_type: None,
                                        },
                                        alias: None,
                                        temporal: None,
                                        with_options: None,
                                        pivot: None,
                                        unpivot: None,
                                        sample: None,
                                    },
                                ],
                                selection: Some(
                                    BinaryOp {
                                        span: Some(
                                            116..117,
                                        ),
                                        op: Gt,
                                        left: ColumnRef {
                                            span: Some(
                                                114..115,
                                            ),
                                            column: ColumnRef {
                                                database: None,
                                                table: None,
                                                column: Name(
                                                    Identifier {
                                                        span: Some(
                                                            114..115,
                                                        ),
                                                        name: "a",
                                                        quote: None,
                                                        ident_type: None,
                                                    },
                                                ),
                                            },
                                        },
                                        right: Literal {
                                            span: Some(
                                                118..119,
                                            ),
                                            value: UInt64(
                                                1,
                                            ),
                                        },
                                    },
                                ),
                                group_by: None,
                                having: None,
                                window_list: None,
                                qualify: None,
                            },
                        ),
                        order_by: [],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                },
            ],
        },
    ),
    body: Select(
        SelectStmt {
            span: Some(
                121..177,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                AliasedExpr {
                    expr: ColumnRef {
                        span: Some(
                            128..130,
                        ),
                        column: ColumnRef {
                            database: None,
                            table: Some(
                                Identifier {
                                    span: Some(
                                        128..130,
                                    ),
                                    name: "t2",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                            column: Name(
                                Identifier {
                                    span: Some(
                                        131..133,
                                    ),
                                    name: "tt",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                        },
                    },
                    alias: None,
                },
                AliasedExpr {
                    expr: ColumnRef {
                        span: Some(
                            135..137,
                        ),
                        column: ColumnRef {
                            database: None,
                            table: Some(
                                Identifier {
                                    span: Some(
                                        135..137,
                                    ),
                                    name: "t3",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                            column: Name(
                                Identifier {
                                    span: Some(
                                        138..139,
                                    ),
                                    name: "a",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                        },
                    },
                    alias: None,
                },
                AliasedExpr {
                    expr: ColumnRef {
                        span: Some(
                            141..143,
                        ),
                        column: ColumnRef {
                            database: None,
                            table: Some(
                                Identifier {
                                    span: Some(
                                        141..143,
                                    ),
                                    name: "t4",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                            column: Name(
                                Identifier {
                                    span: Some(
                                        144..145,
                                    ),
                                    name: "a",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                        },
                    },
                    alias: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        151..153,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            151..153,
                        ),
                        name: "t2",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: None,
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
                Table {
                    span: Some(
                        155..157,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            155..157,
                        ),
                        name: "t3",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: None,
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
                Table {
                    span: Some(
                        159..161,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            159..161,
                        ),
                        name: "t4",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: None,
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
            ],
            selection: Some(
                BinaryOp {
                    span: Some(
                        174..175,
                    ),
                    op: Gt,
                    left: ColumnRef {
                        span: Some(
                            168..170,
                        ),
                        column: ColumnRef {
                            database: None,
                            table: Some(
                                Identifier {
                                    span: Some(
                                        168..170,
                                    ),
                                    name: "t2",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                            column: Name(
                                Identifier {
                                    span: Some(
                                        171..173,
                                    ),
                                    name: "tt",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                        },
                    },
                    right: Literal {
                        span: Some(
                            176..177,
                        ),
                        value: UInt64(
                            1,
                        ),
                    },
                },
            ),
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
with recursive t2(tt) as (select a from t1 union select tt from t2) select t2.tt from t2
---------- Output ---------
WITH RECURSIVE t2(tt) AS (SELECT a FROM t1 UNION SELECT tt FROM t2) SELECT t2.tt FROM t2
---------- AST ------------
Query {
    span: Some(
        68..88,
    ),
    with: Some(
        With {
            span: Some(
                0..67,
            ),
            recursive: true,
            ctes: [
                CTE {
                    span: Some(
                        15..67,
                    ),
                    alias: TableAlias {
                        name: Identifier {
                            span: Some(
                                15..17,
                            ),
                            name: "t2",
                            quote: None,
                            ident_type: None,
                        },
                        columns: [
                            Identifier {
                                span: Some(
                                    18..20,
                                ),
                                name: "tt",
                                quote: None,
                                ident_type: None,
                            },
                        ],
                    },
                    materialized: false,
                    query: Query {
                        span: Some(
                            43..48,
                        ),
                        with: None,
                        body: SetOperation(
                            SetOperation {
                                span: Some(
                                    43..48,
                                ),
                                op: Union,
                                all: false,
                                left: Select(
                                    SelectStmt {
                                        span: Some(
                                            26..42,
                                        ),
                                        hints: None,
                                        distinct: false,
                                        top_n: None,
                                        select_list: [
                                            AliasedExpr {
                                                expr: ColumnRef {
                                                    span: Some(
                                                        33..34,
                                                    ),
                                                    column: ColumnRef {
                                                        database: None,
                                                        table: None,
                                                        column: Name(
                                                            Identifier {
                                                                span: Some(
                                                                    33..34,
                                                                ),
                                                                name: "a",
                                                                quote: None,
                                                                ident_type: None,
                                                            },
                                                        ),
                                                    },
                                                },
                                                alias: None,
                                            },
                                        ],
                                        from: [
                                            Table {
                                                span: Some(
                                                    40..42,
                                                ),
                                                catalog: None,
                                                database: None,
                                                table: Identifier {
                                                    span: Some(
                                                        40..42,
                                                    ),
                                                    name: "t1",
                                                    quote: None,
                                                    ident_type: None,
                                                },
                                                alias: None,
                                                temporal: None,
                                                with_options: None,
                                                pivot: None,
                                                unpivot: None,
                                                sample: None,
                                            },
                                        ],
                                        selection: None,
                                        group_by: None,
                                        having: None,
                                        window_list: None,
                                        qualify: None,
                                    },
                                ),
                                right: Select(
                                    SelectStmt {
                                        span: Some(
                                            49..66,
                                        ),
                                        hints: None,
                                        distinct: false,
                                        top_n: None,
                                        select_list: [
                                            AliasedExpr {
                                                expr: ColumnRef {
                                                    span: Some(
                                                        56..58,
                                                    ),
                                                    column: ColumnRef {
                                                        database: None,
                                                        table: None,
                                                        column: Name(
                                                            Identifier {
                                                                span: Some(
                                                                    56..58,
                                                                ),
                                                                name: "tt",
                                                                quote: None,
                                                                ident_type: None,
                                                            },
                                                        ),
                                                    },
                                                },
                                                alias: None,
                                            },
                                        ],
                                        from: [
                                            Table {
                                                span: Some(
                                                    64..66,
                                                ),
                                                catalog: None,
                                                database: None,
                                                table: Identifier {
                                                    span: Some(
                                                        64..66,
                                                    ),
                                                    name: "t2",
                                                    quote: None,
                                                    ident_type: None,
                                                },
                                                alias: None,
                                                temporal: None,
                                                with_options: None,
                                                pivot: None,
                                                unpivot: None,
                                                sample: None,
                                            },
                                        ],
                                        selection: None,
                                        group_by: None,
                                        having: None,
                                        window_list: None,
                                        qualify: None,
                                    },
                                ),
                            },
                        ),
                        order_by: [],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                },
            ],
        },
    ),
    body: Select(
        SelectStmt {
            span: Some(
                68..88,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                AliasedExpr {
                    expr: ColumnRef {
                        span: Some(
                            75..77,
                        ),
                        column: ColumnRef {
                            database: None,
                            table: Some(
                                Identifier {
                                    span: Some(
                                        75..77,
                                    ),
                                    name: "t2",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                            column: Name(
                                Identifier {
                                    span: Some(
                                        78..80,
                                    ),
                                    name: "tt",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                        },
                    },
                    alias: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        86..88,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            86..88,
                        ),
                        name: "t2",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: None,
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
with t(a,b) as (values(1,1),(2,null),(null,5)) select t.a, t.b from t
---------- Output ---------
WITH t(a, b) AS (VALUES(1, 1), (2, NULL), (NULL, 5)) SELECT t.a, t.b FROM t
---------- AST ------------
Query {
    span: Some(
        47..69,
    ),
    with: Some(
        With {
            span: Some(
                0..46,
            ),
            recursive: false,
            ctes: [
                CTE {
                    span: Some(
                        5..46,
                    ),
                    alias: TableAlias {
                        name: Identifier {
                            span: Some(
                                5..6,
                            ),
                            name: "t",
                            quote: None,
                            ident_type: None,
                        },
                        columns: [
                            Identifier {
                                span: Some(
                                    7..8,
                                ),
                                name: "a",
                                quote: None,
                                ident_type: None,
                            },
                            Identifier {
                                span: Some(
                                    9..10,
                                ),
                                name: "b",
                                quote: None,
                                ident_type: None,
                            },
                        ],
                    },
                    materialized: false,
                    query: Query {
                        span: Some(
                            16..45,
                        ),
                        with: None,
                        body: Values {
                            span: Some(
                                16..45,
                            ),
                            values: [
                                [
                                    Literal {
                                        span: Some(
                                            23..24,
                                        ),
                                        value: UInt64(
                                            1,
                                        ),
                                    },
                                    Literal {
                                        span: Some(
                                            25..26,
                                        ),
                                        value: UInt64(
                                            1,
                                        ),
                                    },
                                ],
                                [
                                    Literal {
                                        span: Some(
                                            29..30,
                                        ),
                                        value: UInt64(
                                            2,
                                        ),
                                    },
                                    Literal {
                                        span: Some(
                                            31..35,
                                        ),
                                        value: Null,
                                    },
                                ],
                                [
                                    Literal {
                                        span: Some(
                                            38..42,
                                        ),
                                        value: Null,
                                    },
                                    Literal {
                                        span: Some(
                                            43..44,
                                        ),
                                        value: UInt64(
                                            5,
                                        ),
                                    },
                                ],
                            ],
                        },
                        order_by: [],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                },
            ],
        },
    ),
    body: Select(
        SelectStmt {
            span: Some(
                47..69,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                AliasedExpr {
                    expr: ColumnRef {
                        span: Some(
                            54..55,
                        ),
                        column: ColumnRef {
                            database: None,
                            table: Some(
                                Identifier {
                                    span: Some(
                                        54..55,
                                    ),
                                    name: "t",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                            column: Name(
                                Identifier {
                                    span: Some(
                                        56..57,
                                    ),
                                    name: "a",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                        },
                    },
                    alias: None,
                },
                AliasedExpr {
                    expr: ColumnRef {
                        span: Some(
                            59..60,
                        ),
                        column: ColumnRef {
                            database: None,
                            table: Some(
                                Identifier {
                                    span: Some(
                                        59..60,
                                    ),
                                    name: "t",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                            column: Name(
                                Identifier {
                                    span: Some(
                                        61..62,
                                    ),
                                    name: "b",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                        },
                    },
                    alias: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        68..69,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            68..69,
                        ),
                        name: "t",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: None,
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select c_count cc, count(*) as custdist, sum(c_acctbal) as totacctbal
from customer, orders ODS,
    (
        select
            c_custkey,
            count(o_orderkey)
        from
            customer left outer join orders on
                c_custkey = o_custkey
                and o_comment not like '%:1%:2%'
        group by
            c_custkey
    ) as c_orders
group by c_count
order by custdist desc nulls first, c_count asc, totacctbal nulls last
limit 10, totacctbal
---------- Output ---------
SELECT c_count AS cc, COUNT(*) AS custdist, sum(c_acctbal) AS totacctbal FROM customer, orders AS ODS, (SELECT c_custkey, count(o_orderkey) FROM customer LEFT OUTER JOIN orders ON c_custkey = o_custkey AND o_comment NOT LIKE '%:1%:2%' GROUP BY c_custkey) AS c_orders GROUP BY c_count ORDER BY custdist DESC NULLS FIRST, c_count ASC, totacctbal NULLS LAST LIMIT 10, totacctbal
---------- AST ------------
Query {
    span: Some(
        0..391,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..391,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                AliasedExpr {
                    expr: ColumnRef {
                        span: Some(
                            7..14,
                        ),
                        column: ColumnRef {
                            database: None,
                            table: None,
                            column: Name(
                                Identifier {
                                    span: Some(
                                        7..14,
                                    ),
                                    name: "c_count",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                        },
                    },
                    alias: Some(
                        Identifier {
                            span: Some(
                                15..17,
                            ),
                            name: "cc",
                            quote: None,
                            ident_type: None,
                        },
                    ),
                },
                AliasedExpr {
                    expr: CountAll {
                        span: Some(
                            19..27,
                        ),
                        qualified: [
                            Star(
                                Some(
                                    25..26,
                                ),
                            ),
                        ],
                        window: None,
                    },
                    alias: Some(
                        Identifier {
                            span: Some(
                                31..39,
                            ),
                            name: "custdist",
                            quote: None,
                            ident_type: None,
                        },
                    ),
                },
                AliasedExpr {
                    expr: FunctionCall {
                        span: Some(
                            41..55,
                        ),
                        func: FunctionCall {
                            distinct: false,
                            name: Identifier {
                                span: Some(
                                    41..44,
                                ),
                                name: "sum",
                                quote: None,
                                ident_type: None,
                            },
                            args: [
                                ColumnRef {
                                    span: Some(
                                        45..54,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    45..54,
                                                ),
                                                name: "c_acctbal",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                            ],
                            params: [],
                            order_by: [],
                            window: None,
                            lambda: None,
                        },
                    },
                    alias: Some(
                        Identifier {
                            span: Some(
                                59..69,
                            ),
                            name: "totacctbal",
                            quote: None,
                            ident_type: None,
                        },
                    ),
                },
            ],
            from: [
                Table {
                    span: Some(
                        75..83,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            75..83,
                        ),
                        name: "customer",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: None,
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
                Table {
                    span: Some(
                        85..95,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            85..91,
                        ),
                        name: "orders",
                        quote: None,
                        ident_type: None,
                    },
                    alias: Some(
                        TableAlias {
                            name: Identifier {
                                span: Some(
                                    92..95,
                                ),
                                name: "ODS",
                                quote: None,
                                ident_type: None,
                            },
                            columns: [],
                        },
                    ),
                    temporal: None,
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
                Subquery {
                    span: Some(
                        101..374,
                    ),
                    lateral: false,
                    subquery: Query {
                        span: Some(
                            111..356,
                        ),
                        with: None,
                        body: Select(
                            SelectStmt {
                                span: Some(
                                    111..356,
                                ),
                                hints: None,
                                distinct: false,
                                top_n: None,
                                select_list: [
                                    AliasedExpr {
                                        expr: ColumnRef {
                                            span: Some(
                                                130..139,
                                            ),
                                            column: ColumnRef {
                                                database: None,
                                                table: None,
                                                column: Name(
                                                    Identifier {
                                                        span: Some(
                                                            130..139,
                                                        ),
                                                        name: "c_custkey",
                                                        quote: None,
                                                        ident_type: None,
                                                    },
                                                ),
                                            },
                                        },
                                        alias: None,
                                    },
                                    AliasedExpr {
                                        expr: FunctionCall {
                                            span: Some(
                                                153..170,
                                            ),
                                            func: FunctionCall {
                                                distinct: false,
                                                name: Identifier {
                                                    span: Some(
                                                        153..158,
                                                    ),
                                                    name: "count",
                                                    quote: None,
                                                    ident_type: None,
                                                },
                                                args: [
                                                    ColumnRef {
                                                        span: Some(
                                                            159..169,
                                                        ),
                                                        column: ColumnRef {
                                                            database: None,
                                                            table: None,
                                                            column: Name(
                                                                Identifier {
                                                                    span: Some(
                                                                        159..169,
                                                                    ),
                                                                    name: "o_orderkey",
                                                                    quote: None,
                                                                    ident_type: None,
                                                                },
                                                            ),
                                                        },
                                                    },
                                                ],
                                                params: [],
                                                order_by: [],
                                                window: None,
                                                lambda: None,
                                            },
                                        },
                                        alias: None,
                                    },
                                ],
                                from: [
                                    Join {
                                        span: Some(
                                            205..220,
                                        ),
                                        join: Join {
                                            op: LeftOuter,
                                            condition: On(
                                                BinaryOp {
                                                    span: Some(
                                                        285..288,
                                                    ),
                                                    op: And,
                                                    left: BinaryOp {
                                                        span: Some(
                                                            257..258,
                                                        ),
                                                        op: Eq,
                                                        left: ColumnRef {
                                                            span: Some(
                                                                247..256,
                                                            ),
                                                            column: ColumnRef {
                                                                database: None,
                                                                table: None,
                                                                column: Name(
                                                                    Identifier {
                                                                        span: Some(
                                                                            247..256,
                                                                        ),
                                                                        name: "c_custkey",
                                                                        quote: None,
                                                                        ident_type: None,
                                                                    },
                                                                ),
                                                            },
                                                        },
                                                        right: ColumnRef {
                                                            span: Some(
                                                                259..268,
                                                            ),
                                                            column: ColumnRef {
                                                                database: None,
                                                                table: None,
                                                                column: Name(
                                                                    Identifier {
                                                                        span: Some(
                                                                            259..268,
                                                                        ),
                                                                        name: "o_custkey",
                                                                        quote: None,
                                                                        ident_type: None,
                                                                    },
                                                                ),
                                                            },
                                                        },
                                                    },
                                                    right: BinaryOp {
                                                        span: Some(
                                                            299..307,
                                                        ),
                                                        op: NotLike,
                                                        left: ColumnRef {
                                                            span: Some(
                                                                289..298,
                                                            ),
                                                            column: ColumnRef {
                                                                database: None,
                                                                table: None,
                                                                column: Name(
                                                                    Identifier {
                                                                        span: Some(
                                                                            289..298,
                                                                        ),
                                                                        name: "o_comment",
                                                                        quote: None,
                                                                        ident_type: None,
                                                                    },
                                                                ),
                                                            },
                                                        },
                                                        right: Literal {
                                                            span: Some(
                                                                308..317,
                                                            ),
                                                            value: String(
                                                                "%:1%:2%",
                                                            ),
                                                        },
                                                    },
                                                },
                                            ),
                                            left: Table {
                                                span: Some(
                                                    196..204,
                                                ),
                                                catalog: None,
                                                database: None,
                                                table: Identifier {
                                                    span: Some(
                                                        196..204,
                                                    ),
                                                    name: "customer",
                                                    quote: None,
                                                    ident_type: None,
                                                },
                                                alias: None,
                                                temporal: None,
                                                with_options: None,
                                                pivot: None,
                                                unpivot: None,
                                                sample: None,
                                            },
                                            right: Table {
                                                span: Some(
                                                    221..227,
                                                ),
                                                catalog: None,
                                                database: None,
                                                table: Identifier {
                                                    span: Some(
                                                        221..227,
                                                    ),
                                                    name: "orders",
                                                    quote: None,
                                                    ident_type: None,
                                                },
                                                alias: None,
                                                temporal: None,
                                                with_options: None,
                                                pivot: None,
                                                unpivot: None,
                                                sample: None,
                                            },
                                        },
                                    },
                                ],
                                selection: None,
                                group_by: Some(
                                    Normal(
                                        [
                                            ColumnRef {
                                                span: Some(
                                                    347..356,
                                                ),
                                                column: ColumnRef {
                                                    database: None,
                                                    table: None,
                                                    column: Name(
                                                        Identifier {
                                                            span: Some(
                                                                347..356,
                                                            ),
                                                            name: "c_custkey",
                                                            quote: None,
                                                            ident_type: None,
                                                        },
                                                    ),
                                                },
                                            },
                                        ],
                                    ),
                                ),
                                having: None,
                                window_list: None,
                                qualify: None,
                            },
                        ),
                        order_by: [],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                    alias: Some(
                        TableAlias {
                            name: Identifier {
                                span: Some(
                                    366..374,
                                ),
                                name: "c_orders",
                                quote: None,
                                ident_type: None,
                            },
                            columns: [],
                        },
                    ),
                    pivot: None,
                    unpivot: None,
                },
            ],
            selection: None,
            group_by: Some(
                Normal(
                    [
                        ColumnRef {
                            span: Some(
                                384..391,
                            ),
                            column: ColumnRef {
                                database: None,
                                table: None,
                                column: Name(
                                    Identifier {
                                        span: Some(
                                            384..391,
                                        ),
                                        name: "c_count",
                                        quote: None,
                                        ident_type: None,
                                    },
                                ),
                            },
                        },
                    ],
                ),
            ),
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [
        OrderByExpr {
            expr: ColumnRef {
                span: Some(
                    401..409,
                ),
                column: ColumnRef {
                    database: None,
                    table: None,
                    column: Name(
                        Identifier {
                            span: Some(
                                401..409,
                            ),
                            name: "custdist",
                            quote: None,
                            ident_type: None,
                        },
                    ),
                },
            },
            asc: Some(
                false,
            ),
            nulls_first: Some(
                true,
            ),
        },
        OrderByExpr {
            expr: ColumnRef {
                span: Some(
                    428..435,
                ),
                column: ColumnRef {
                    database: None,
                    table: None,
                    column: Name(
                        Identifier {
                            span: Some(
                                428..435,
                            ),
                            name: "c_count",
                            quote: None,
                            ident_type: None,
                        },
                    ),
                },
            },
            asc: Some(
                true,
            ),
            nulls_first: None,
        },
        OrderByExpr {
            expr: ColumnRef {
                span: Some(
                    441..451,
                ),
                column: ColumnRef {
                    database: None,
                    table: None,
                    column: Name(
                        Identifier {
                            span: Some(
                                441..451,
                            ),
                            name: "totacctbal",
                            quote: None,
                            ident_type: None,
                        },
                    ),
                },
            },
            asc: None,
            nulls_first: Some(
                false,
            ),
        },
    ],
    limit: [
        Literal {
            span: Some(
                469..471,
            ),
            value: UInt64(
                10,
            ),
        },
        ColumnRef {
            span: Some(
                473..483,
            ),
            column: ColumnRef {
                database: None,
                table: None,
                column: Name(
                    Identifier {
                        span: Some(
                            473..483,
                        ),
                        name: "totacctbal",
                        quote: None,
                        ident_type: None,
                    },
                ),
            },
        },
    ],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from t1 union select * from t2
---------- Output ---------
SELECT * FROM t1 UNION SELECT * FROM t2
---------- AST ------------
Query {
    span: Some(
        17..22,
    ),
    with: None,
    body: SetOperation(
        SetOperation {
            span: Some(
                17..22,
            ),
            op: Union,
            all: false,
            left: Select(
                SelectStmt {
                    span: Some(
                        0..16,
                    ),
                    hints: None,
                    distinct: false,
                    top_n: None,
                    select_list: [
                        StarColumns {
                            qualified: [
                                Star(
                                    Some(
                                        7..8,
                                    ),
                                ),
                            ],
                            column_filter: None,
                        },
                    ],
                    from: [
                        Table {
                            span: Some(
                                14..16,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    14..16,
                                ),
                                name: "t1",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    ],
                    selection: None,
                    group_by: None,
                    having: None,
                    window_list: None,
                    qualify: None,
                },
            ),
            right: Select(
                SelectStmt {
                    span: Some(
                        23..39,
                    ),
                    hints: None,
                    distinct: false,
                    top_n: None,
                    select_list: [
                        StarColumns {
                            qualified: [
                                Star(
                                    Some(
                                        30..31,
                                    ),
                                ),
                            ],
                            column_filter: None,
                        },
                    ],
                    from: [
                        Table {
                            span: Some(
                                37..39,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    37..39,
                                ),
                                name: "t2",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    ],
                    selection: None,
                    group_by: None,
                    having: None,
                    window_list: None,
                    qualify: None,
                },
            ),
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from t1 except select * from t2
---------- Output ---------
SELECT * FROM t1 EXCEPT SELECT * FROM t2
---------- AST ------------
Query {
    span: Some(
        17..23,
    ),
    with: None,
    body: SetOperation(
        SetOperation {
            span: Some(
                17..23,
            ),
            op: Except,
            all: false,
            left: Select(
                SelectStmt {
                    span: Some(
                        0..16,
                    ),
                    hints: None,
                    distinct: false,
                    top_n: None,
                    select_list: [
                        StarColumns {
                            qualified: [
                                Star(
                                    Some(
                                        7..8,
                                    ),
                                ),
                            ],
                            column_filter: None,
                        },
                    ],
                    from: [
                        Table {
                            span: Some(
                                14..16,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    14..16,
                                ),
                                name: "t1",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    ],
                    selection: None,
                    group_by: None,
                    having: None,
                    window_list: None,
                    qualify: None,
                },
            ),
            right: Select(
                SelectStmt {
                    span: Some(
                        24..40,
                    ),
                    hints: None,
                    distinct: false,
                    top_n: None,
                    select_list: [
                        StarColumns {
                            qualified: [
                                Star(
                                    Some(
                                        31..32,
                                    ),
                                ),
                            ],
                            column_filter: None,
                        },
                    ],
                    from: [
                        Table {
                            span: Some(
                                38..40,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    38..40,
                                ),
                                name: "t2",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    ],
                    selection: None,
                    group_by: None,
                    having: None,
                    window_list: None,
                    qualify: None,
                },
            ),
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from t1 union select * from t2 union select * from t3
---------- Output ---------
SELECT * FROM t1 UNION SELECT * FROM t2 UNION SELECT * FROM t3
---------- AST ------------
Query {
    span: Some(
        40..45,
    ),
    with: None,
    body: SetOperation(
        SetOperation {
            span: Some(
                40..45,
            ),
            op: Union,
            all: false,
            left: SetOperation(
                SetOperation {
                    span: Some(
                        17..22,
                    ),
                    op: Union,
                    all: false,
                    left: Select(
                        SelectStmt {
                            span: Some(
                                0..16,
                            ),
                            hints: None,
                            distinct: false,
                            top_n: None,
                            select_list: [
                                StarColumns {
                                    qualified: [
                                        Star(
                                            Some(
                                                7..8,
                                            ),
                                        ),
                                    ],
                                    column_filter: None,
                                },
                            ],
                            from: [
                                Table {
                                    span: Some(
                                        14..16,
                                    ),
                                    catalog: None,
                                    database: None,
                                    table: Identifier {
                                        span: Some(
                                            14..16,
                                        ),
                                        name: "t1",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                    temporal: None,
                                    with_options: None,
                                    pivot: None,
                                    unpivot: None,
                                    sample: None,
                                },
                            ],
                            selection: None,
                            group_by: None,
                            having: None,
                            window_list: None,
                            qualify: None,
                        },
                    ),
                    right: Select(
                        SelectStmt {
                            span: Some(
                                23..39,
                            ),
                            hints: None,
                            distinct: false,
                            top_n: None,
                            select_list: [
                                StarColumns {
                                    qualified: [
                                        Star(
                                            Some(
                                                30..31,
                                            ),
                                        ),
                                    ],
                                    column_filter: None,
                                },
                            ],
                            from: [
                                Table {
                                    span: Some(
                                        37..39,
                                    ),
                                    catalog: None,
                                    database: None,
                                    table: Identifier {
                                        span: Some(
                                            37..39,
                                        ),
                                        name: "t2",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                    temporal: None,
                                    with_options: None,
                                    pivot: None,
                                    unpivot: None,
                                    sample: None,
                                },
                            ],
                            selection: None,
                            group_by: None,
                            having: None,
                            window_list: None,
                            qualify: None,
                        },
                    ),
                },
            ),
            right: Select(
                SelectStmt {
                    span: Some(
                        46..62,
                    ),
                    hints: None,
                    distinct: false,
                    top_n: None,
                    select_list: [
                        StarColumns {
                            qualified: [
                                Star(
                                    Some(
                                        53..54,
                                    ),
                                ),
                            ],
                            column_filter: None,
                        },
                    ],
                    from: [
                        Table {
                            span: Some(
                                60..62,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    60..62,
                                ),
                                name: "t3",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    ],
                    selection: None,
                    group_by: None,
                    having: None,
                    window_list: None,
                    qualify: None,
                },
            ),
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from t1 union select * from t2 union all select * from t3
---------- Output ---------
SELECT * FROM t1 UNION SELECT * FROM t2 UNION ALL SELECT * FROM t3
---------- AST ------------
Query {
    span: Some(
        40..49,
    ),
    with: None,
    body: SetOperation(
        SetOperation {
            span: Some(
                40..49,
            ),
            op: Union,
            all: true,
            left: SetOperation(
                SetOperation {
                    span: Some(
                        17..22,
                    ),
                    op: Union,
                    all: false,
                    left: Select(
                        SelectStmt {
                            span: Some(
                                0..16,
                            ),
                            hints: None,
                            distinct: false,
                            top_n: None,
                            select_list: [
                                StarColumns {
                                    qualified: [
                                        Star(
                                            Some(
                                                7..8,
                                            ),
                                        ),
                                    ],
                                    column_filter: None,
                                },
                            ],
                            from: [
                                Table {
                                    span: Some(
                                        14..16,
                                    ),
                                    catalog: None,
                                    database: None,
                                    table: Identifier {
                                        span: Some(
                                            14..16,
                                        ),
                                        name: "t1",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                    temporal: None,
                                    with_options: None,
                                    pivot: None,
                                    unpivot: None,
                                    sample: None,
                                },
                            ],
                            selection: None,
                            group_by: None,
                            having: None,
                            window_list: None,
                            qualify: None,
                        },
                    ),
                    right: Select(
                        SelectStmt {
                            span: Some(
                                23..39,
                            ),
                            hints: None,
                            distinct: false,
                            top_n: None,
                            select_list: [
                                StarColumns {
                                    qualified: [
                                        Star(
                                            Some(
                                                30..31,
                                            ),
                                        ),
                                    ],
                                    column_filter: None,
                                },
                            ],
                            from: [
                                Table {
                                    span: Some(
                                        37..39,
                                    ),
                                    catalog: None,
                                    database: None,
                                    table: Identifier {
                                        span: Some(
                                            37..39,
                                        ),
                                        name: "t2",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                    temporal: None,
                                    with_options: None,
                                    pivot: None,
                                    unpivot: None,
                                    sample: None,
                                },
                            ],
                            selection: None,
                            group_by: None,
                            having: None,
                            window_list: None,
                            qualify: None,
                        },
                    ),
                },
            ),
            right: Select(
                SelectStmt {
                    span: Some(
                        50..66,
                    ),
                    hints: None,
                    distinct: false,
                    top_n: None,
                    select_list: [
                        StarColumns {
                            qualified: [
                                Star(
                                    Some(
                                        57..58,
                                    ),
                                ),
                            ],
                            column_filter: None,
                        },
                    ],
                    from: [
                        Table {
                            span: Some(
                                64..66,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    64..66,
                                ),
                                name: "t3",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    ],
                    selection: None,
                    group_by: None,
                    having: None,
                    window_list: None,
                    qualify: None,
                },
            ),
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from (
        (SELECT f, g FROM union_fuzz_result1
        EXCEPT
        SELECT f, g FROM union_fuzz_result2)
        UNION ALL
        (SELECT f, g FROM union_fuzz_result2
        EXCEPT
        SELECT f, g FROM union_fuzz_result1)
)
---------- Output ---------
SELECT * FROM ((SELECT f, g FROM union_fuzz_result1 EXCEPT SELECT f, g FROM union_fuzz_result2) UNION ALL (SELECT f, g FROM union_fuzz_result2 EXCEPT SELECT f, g FROM union_fuzz_result1))
---------- AST ------------
Query {
    span: Some(
        0..245,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..245,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Subquery {
                    span: Some(
                        14..245,
                    ),
                    lateral: false,
                    subquery: Query {
                        span: Some(
                            129..138,
                        ),
                        with: None,
                        body: SetOperation(
                            SetOperation {
                                span: Some(
                                    129..138,
                                ),
                                op: Union,
                                all: true,
                                left: SetOperation(
                                    SetOperation {
                                        span: Some(
                                            69..75,
                                        ),
                                        op: Except,
                                        all: false,
                                        left: Select(
                                            SelectStmt {
                                                span: Some(
                                                    25..60,
                                                ),
                                                hints: None,
                                                distinct: false,
                                                top_n: None,
                                                select_list: [
                                                    AliasedExpr {
                                                        expr: ColumnRef {
                                                            span: Some(
                                                                32..33,
                                                            ),
                                                            column: ColumnRef {
                                                                database: None,
                                                                table: None,
                                                                column: Name(
                                                                    Identifier {
                                                                        span: Some(
                                                                            32..33,
                                                                        ),
                                                                        name: "f",
                                                                        quote: None,
                                                                        ident_type: None,
                                                                    },
                                                                ),
                                                            },
                                                        },
                                                        alias: None,
                                                    },
                                                    AliasedExpr {
                                                        expr: ColumnRef {
                                                            span: Some(
                                                                35..36,
                                                            ),
                                                            column: ColumnRef {
                                                                database: None,
                                                                table: None,
                                                                column: Name(
                                                                    Identifier {
                                                                        span: Some(
                                                                            35..36,
                                                                        ),
                                                                        name: "g",
                                                                        quote: None,
                                                                        ident_type: None,
                                                                    },
                                                                ),
                                                            },
                                                        },
                                                        alias: None,
                                                    },
                                                ],
                                                from: [
                                                    Table {
                                                        span: Some(
                                                            42..60,
                                                        ),
                                                        catalog: None,
                                                        database: None,
                                                        table: Identifier {
                                                            span: Some(
                                                                42..60,
                                                            ),
                                                            name: "union_fuzz_result1",
                                                            quote: None,
                                                            ident_type: None,
                                                        },
                                                        alias: None,
                                                        temporal: None,
                                                        with_options: None,
                                                        pivot: None,
                                                        unpivot: None,
                                                        sample: None,
                                                    },
                                                ],
                                                selection: None,
                                                group_by: None,
                                                having: None,
                                                window_list: None,
                                                qualify: None,
                                            },
                                        ),
                                        right: Select(
                                            SelectStmt {
                                                span: Some(
                                                    84..119,
                                                ),
                                                hints: None,
                                                distinct: false,
                                                top_n: None,
                                                select_list: [
                                                    AliasedExpr {
                                                        expr: ColumnRef {
                                                            span: Some(
                                                                91..92,
                                                            ),
                                                            column: ColumnRef {
                                                                database: None,
                                                                table: None,
                                                                column: Name(
                                                                    Identifier {
                                                                        span: Some(
                                                                            91..92,
                                                                        ),
                                                                        name: "f",
                                                                        quote: None,
                                                                        ident_type: None,
                                                                    },
                                                                ),
                                                            },
                                                        },
                                                        alias: None,
                                                    },
                                                    AliasedExpr {
                                                        expr: ColumnRef {
                                                            span: Some(
                                                                94..95,
                                                            ),
                                                            column: ColumnRef {
                                                                database: None,
                                                                table: None,
                                                                column: Name(
                                                                    Identifier {
                                                                        span: Some(
                                                                            94..95,
                                                                        ),
                                                                        name: "g",
                                                                        quote: None,
                                                                        ident_type: None,
                                                                    },
                                                                ),
                                                            },
                                                        },
                                                        alias: None,
                                                    },
                                                ],
                                                from: [
                                                    Table {
                                                        span: Some(
                                                            101..119,
                                                        ),
                                                        catalog: None,
                                                        database: None,
                                                        table: Identifier {
                                                            span: Some(
                                                                101..119,
                                                            ),
                                                            name: "union_fuzz_result2",
                                                            quote: None,
                                                            ident_type: None,
                                                        },
                                                        alias: None,
                                                        temporal: None,
                                                        with_options: None,
                                                        pivot: None,
                                                        unpivot: None,
                                                        sample: None,
                                                    },
                                                ],
                                                selection: None,
                                                group_by: None,
                                                having: None,
                                                window_list: None,
                                                qualify: None,
                                            },
                                        ),
                                    },
                                ),
                                right: SetOperation(
                                    SetOperation {
                                        span: Some(
                                            192..198,
                                        ),
                                        op: Except,
                                        all: false,
                                        left: Select(
                                            SelectStmt {
                                                span: Some(
                                                    148..183,
                                                ),
                                                hints: None,
                                                distinct: false,
                                                top_n: None,
                                                select_list: [
                                                    AliasedExpr {
                                                        expr: ColumnRef {
                                                            span: Some(
                                                                155..156,
                                                            ),
                                                            column: ColumnRef {
                                                                database: None,
                                                                table: None,
                                                                column: Name(
                                                                    Identifier {
                                                                        span: Some(
                                                                            155..156,
                                                                        ),
                                                                        name: "f",
                                                                        quote: None,
                                                                        ident_type: None,
                                                                    },
                                                                ),
                                                            },
                                                        },
                                                        alias: None,
                                                    },
                                                    AliasedExpr {
                                                        expr: ColumnRef {
                                                            span: Some(
                                                                158..159,
                                                            ),
                                                            column: ColumnRef {
                                                                database: None,
                                                                table: None,
                                                                column: Name(
                                                                    Identifier {
                                                                        span: Some(
                                                                            158..159,
                                                                        ),
                                                                        name: "g",
                                                                        quote: None,
                                                                        ident_type: None,
                                                                    },
                                                                ),
                                                            },
                                                        },
                                                        alias: None,
                                                    },
                                                ],
                                                from: [
                                                    Table {
                                                        span: Some(
                                                            165..183,
                                                        ),
                                                        catalog: None,
                                                        database: None,
                                                        table: Identifier {
                                                            span: Some(
                                                                165..183,
                                                            ),
                                                            name: "union_fuzz_result2",
                                                            quote: None,
                                                            ident_type: None,
                                                        },
                                                        alias: None,
                                                        temporal: None,
                                                        with_options: None,
                                                        pivot: None,
                                                        unpivot: None,
                                                        sample: None,
                                                    },
                                                ],
                                                selection: None,
                                                group_by: None,
                                                having: None,
                                                window_list: None,
                                                qualify: None,
                                            },
                                        ),
                                        right: Select(
                                            SelectStmt {
                                                span: Some(
                                                    207..242,
                                                ),
                                                hints: None,
                                                distinct: false,
                                                top_n: None,
                                                select_list: [
                                                    AliasedExpr {
                                                        expr: ColumnRef {
                                                            span: Some(
                                                                214..215,
                                                            ),
                                                            column: ColumnRef {
                                                                database: None,
                                                                table: None,
                                                                column: Name(
                                                                    Identifier {
                                                                        span: Some(
                                                                            214..215,
                                                                        ),
                                                                        name: "f",
                                                                        quote: None,
                                                                        ident_type: None,
                                                                    },
                                                                ),
                                                            },
                                                        },
                                                        alias: None,
                                                    },
                                                    AliasedExpr {
                                                        expr: ColumnRef {
                                                            span: Some(
                                                                217..218,
                                                            ),
                                                            column: ColumnRef {
                                                                database: None,
                                                                table: None,
                                                                column: Name(
                                                                    Identifier {
                                                                        span: Some(
                                                                            217..218,
                                                                        ),
                                                                        name: "g",
                                                                        quote: None,
                                                                        ident_type: None,
                                                                    },
                                                                ),
                                                            },
                                                        },
                                                        alias: None,
                                                    },
                                                ],
                                                from: [
                                                    Table {
                                                        span: Some(
                                                            224..242,
                                                        ),
                                                        catalog: None,
                                                        database: None,
                                                        table: Identifier {
                                                            span: Some(
                                                                224..242,
                                                            ),
                                                            name: "union_fuzz_result1",
                                                            quote: None,
                                                            ident_type: None,
                                                        },
                                                        alias: None,
                                                        temporal: None,
                                                        with_options: None,
                                                        pivot: None,
                                                        unpivot: None,
                                                        sample: None,
                                                    },
                                                ],
                                                selection: None,
                                                group_by: None,
                                                having: None,
                                                window_list: None,
                                                qualify: None,
                                            },
                                        ),
                                    },
                                ),
                            },
                        ),
                        order_by: [],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                    alias: None,
                    pivot: None,
                    unpivot: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from t1 union select * from t2 intersect select * from t3
---------- Output ---------
SELECT * FROM t1 UNION SELECT * FROM t2 INTERSECT SELECT * FROM t3
---------- AST ------------
Query {
    span: Some(
        17..22,
    ),
    with: None,
    body: SetOperation(
        SetOperation {
            span: Some(
                17..22,
            ),
            op: Union,
            all: false,
            left: Select(
                SelectStmt {
                    span: Some(
                        0..16,
                    ),
                    hints: None,
                    distinct: false,
                    top_n: None,
                    select_list: [
                        StarColumns {
                            qualified: [
                                Star(
                                    Some(
                                        7..8,
                                    ),
                                ),
                            ],
                            column_filter: None,
                        },
                    ],
                    from: [
                        Table {
                            span: Some(
                                14..16,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    14..16,
                                ),
                                name: "t1",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    ],
                    selection: None,
                    group_by: None,
                    having: None,
                    window_list: None,
                    qualify: None,
                },
            ),
            right: SetOperation(
                SetOperation {
                    span: Some(
                        40..49,
                    ),
                    op: Intersect,
                    all: false,
                    left: Select(
                        SelectStmt {
                            span: Some(
                                23..39,
                            ),
                            hints: None,
                            distinct: false,
                            top_n: None,
                            select_list: [
                                StarColumns {
                                    qualified: [
                                        Star(
                                            Some(
                                                30..31,
                                            ),
                                        ),
                                    ],
                                    column_filter: None,
                                },
                            ],
                            from: [
                                Table {
                                    span: Some(
                                        37..39,
                                    ),
                                    catalog: None,
                                    database: None,
                                    table: Identifier {
                                        span: Some(
                                            37..39,
                                        ),
                                        name: "t2",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                    temporal: None,
                                    with_options: None,
                                    pivot: None,
                                    unpivot: None,
                                    sample: None,
                                },
                            ],
                            selection: None,
                            group_by: None,
                            having: None,
                            window_list: None,
                            qualify: None,
                        },
                    ),
                    right: Select(
                        SelectStmt {
                            span: Some(
                                50..66,
                            ),
                            hints: None,
                            distinct: false,
                            top_n: None,
                            select_list: [
                                StarColumns {
                                    qualified: [
                                        Star(
                                            Some(
                                                57..58,
                                            ),
                                        ),
                                    ],
                                    column_filter: None,
                                },
                            ],
                            from: [
                                Table {
                                    span: Some(
                                        64..66,
                                    ),
                                    catalog: None,
                                    database: None,
                                    table: Identifier {
                                        span: Some(
                                            64..66,
                                        ),
                                        name: "t3",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                    temporal: None,
                                    with_options: None,
                                    pivot: None,
                                    unpivot: None,
                                    sample: None,
                                },
                            ],
                            selection: None,
                            group_by: None,
                            having: None,
                            window_list: None,
                            qualify: None,
                        },
                    ),
                },
            ),
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
(select * from t1 union select * from t2) intersect select * from t3
---------- Output ---------
(SELECT * FROM t1 UNION SELECT * FROM t2) INTERSECT SELECT * FROM t3
---------- AST ------------
Query {
    span: Some(
        42..51,
    ),
    with: None,
    body: SetOperation(
        SetOperation {
            span: Some(
                42..51,
            ),
            op: Intersect,
            all: false,
            left: SetOperation(
                SetOperation {
                    span: Some(
                        18..23,
                    ),
                    op: Union,
                    all: false,
                    left: Select(
                        SelectStmt {
                            span: Some(
                                1..17,
                            ),
                            hints: None,
                            distinct: false,
                            top_n: None,
                            select_list: [
                                StarColumns {
                                    qualified: [
                                        Star(
                                            Some(
                                                8..9,
                                            ),
                                        ),
                                    ],
                                    column_filter: None,
                                },
                            ],
                            from: [
                                Table {
                                    span: Some(
                                        15..17,
                                    ),
                                    catalog: None,
                                    database: None,
                                    table: Identifier {
                                        span: Some(
                                            15..17,
                                        ),
                                        name: "t1",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                    temporal: None,
                                    with_options: None,
                                    pivot: None,
                                    unpivot: None,
                                    sample: None,
                                },
                            ],
                            selection: None,
                            group_by: None,
                            having: None,
                            window_list: None,
                            qualify: None,
                        },
                    ),
                    right: Select(
                        SelectStmt {
                            span: Some(
                                24..40,
                            ),
                            hints: None,
                            distinct: false,
                            top_n: None,
                            select_list: [
                                StarColumns {
                                    qualified: [
                                        Star(
                                            Some(
                                                31..32,
                                            ),
                                        ),
                                    ],
                                    column_filter: None,
                                },
                            ],
                            from: [
                                Table {
                                    span: Some(
                                        38..40,
                                    ),
                                    catalog: None,
                                    database: None,
                                    table: Identifier {
                                        span: Some(
                                            38..40,
                                        ),
                                        name: "t2",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                    temporal: None,
                                    with_options: None,
                                    pivot: None,
                                    unpivot: None,
                                    sample: None,
                                },
                            ],
                            selection: None,
                            group_by: None,
                            having: None,
                            window_list: None,
                            qualify: None,
                        },
                    ),
                },
            ),
            right: Select(
                SelectStmt {
                    span: Some(
                        52..68,
                    ),
                    hints: None,
                    distinct: false,
                    top_n: None,
                    select_list: [
                        StarColumns {
                            qualified: [
                                Star(
                                    Some(
                                        59..60,
                                    ),
                                ),
                            ],
                            column_filter: None,
                        },
                    ],
                    from: [
                        Table {
                            span: Some(
                                66..68,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    66..68,
                                ),
                                name: "t3",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    ],
                    selection: None,
                    group_by: None,
                    having: None,
                    window_list: None,
                    qualify: None,
                },
            ),
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
(select * from t1 union select * from t2) union select * from t3
---------- Output ---------
SELECT * FROM t1 UNION SELECT * FROM t2 UNION SELECT * FROM t3
---------- AST ------------
Query {
    span: Some(
        42..47,
    ),
    with: None,
    body: SetOperation(
        SetOperation {
            span: Some(
                42..47,
            ),
            op: Union,
            all: false,
            left: SetOperation(
                SetOperation {
                    span: Some(
                        18..23,
                    ),
                    op: Union,
                    all: false,
                    left: Select(
                        SelectStmt {
                            span: Some(
                                1..17,
                            ),
                            hints: None,
                            distinct: false,
                            top_n: None,
                            select_list: [
                                StarColumns {
                                    qualified: [
                                        Star(
                                            Some(
                                                8..9,
                                            ),
                                        ),
                                    ],
                                    column_filter: None,
                                },
                            ],
                            from: [
                                Table {
                                    span: Some(
                                        15..17,
                                    ),
                                    catalog: None,
                                    database: None,
                                    table: Identifier {
                                        span: Some(
                                            15..17,
                                        ),
                                        name: "t1",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                    temporal: None,
                                    with_options: None,
                                    pivot: None,
                                    unpivot: None,
                                    sample: None,
                                },
                            ],
                            selection: None,
                            group_by: None,
                            having: None,
                            window_list: None,
                            qualify: None,
                        },
                    ),
                    right: Select(
                        SelectStmt {
                            span: Some(
                                24..40,
                            ),
                            hints: None,
                            distinct: false,
                            top_n: None,
                            select_list: [
                                StarColumns {
                                    qualified: [
                                        Star(
                                            Some(
                                                31..32,
                                            ),
                                        ),
                                    ],
                                    column_filter: None,
                                },
                            ],
                            from: [
                                Table {
                                    span: Some(
                                        38..40,
                                    ),
                                    catalog: None,
                                    database: None,
                                    table: Identifier {
                                        span: Some(
                                            38..40,
                                        ),
                                        name: "t2",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                    temporal: None,
                                    with_options: None,
                                    pivot: None,
                                    unpivot: None,
                                    sample: None,
                                },
                            ],
                            selection: None,
                            group_by: None,
                            having: None,
                            window_list: None,
                            qualify: None,
                        },
                    ),
                },
            ),
            right: Select(
                SelectStmt {
                    span: Some(
                        48..64,
                    ),
                    hints: None,
                    distinct: false,
                    top_n: None,
                    select_list: [
                        StarColumns {
                            qualified: [
                                Star(
                                    Some(
                                        55..56,
                                    ),
                                ),
                            ],
                            column_filter: None,
                        },
                    ],
                    from: [
                        Table {
                            span: Some(
                                62..64,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    62..64,
                                ),
                                name: "t3",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    ],
                    selection: None,
                    group_by: None,
                    having: None,
                    window_list: None,
                    qualify: None,
                },
            ),
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from t1 union (select * from t2 union select * from t3)
---------- Output ---------
SELECT * FROM t1 UNION SELECT * FROM t2 UNION SELECT * FROM t3
---------- AST ------------
Query {
    span: Some(
        17..22,
    ),
    with: None,
    body: SetOperation(
        SetOperation {
            span: Some(
                17..22,
            ),
            op: Union,
            all: false,
            left: Select(
                SelectStmt {
                    span: Some(
                        0..16,
                    ),
                    hints: None,
                    distinct: false,
                    top_n: None,
                    select_list: [
                        StarColumns {
                            qualified: [
                                Star(
                                    Some(
                                        7..8,
                                    ),
                                ),
                            ],
                            column_filter: None,
                        },
                    ],
                    from: [
                        Table {
                            span: Some(
                                14..16,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    14..16,
                                ),
                                name: "t1",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                    ],
                    selection: None,
                    group_by: None,
                    having: None,
                    window_list: None,
                    qualify: None,
                },
            ),
            right: SetOperation(
                SetOperation {
                    span: Some(
                        41..46,
                    ),
                    op: Union,
                    all: false,
                    left: Select(
                        SelectStmt {
                            span: Some(
                                24..40,
                            ),
                            hints: None,
                            distinct: false,
                            top_n: None,
                            select_list: [
                                StarColumns {
                                    qualified: [
                                        Star(
                                            Some(
                                                31..32,
                                            ),
                                        ),
                                    ],
                                    column_filter: None,
                                },
                            ],
                            from: [
                                Table {
                                    span: Some(
                                        38..40,
                                    ),
                                    catalog: None,
                                    database: None,
                                    table: Identifier {
                                        span: Some(
                                            38..40,
                                        ),
                                        name: "t2",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                    temporal: None,
                                    with_options: None,
                                    pivot: None,
                                    unpivot: None,
                                    sample: None,
                                },
                            ],
                            selection: None,
                            group_by: None,
                            having: None,
                            window_list: None,
                            qualify: None,
                        },
                    ),
                    right: Select(
                        SelectStmt {
                            span: Some(
                                47..63,
                            ),
                            hints: None,
                            distinct: false,
                            top_n: None,
                            select_list: [
                                StarColumns {
                                    qualified: [
                                        Star(
                                            Some(
                                                54..55,
                                            ),
                                        ),
                                    ],
                                    column_filter: None,
                                },
                            ],
                            from: [
                                Table {
                                    span: Some(
                                        61..63,
                                    ),
                                    catalog: None,
                                    database: None,
                                    table: Identifier {
                                        span: Some(
                                            61..63,
                                        ),
                                        name: "t3",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                    temporal: None,
                                    with_options: None,
                                    pivot: None,
                                    unpivot: None,
                                    sample: None,
                                },
                            ],
                            selection: None,
                            group_by: None,
                            having: None,
                            window_list: None,
                            qualify: None,
                        },
                    ),
                },
            ),
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
SELECT * FROM ((SELECT *) EXCEPT (SELECT *)) foo
---------- Output ---------
SELECT * FROM (SELECT * EXCEPT SELECT *) AS foo
---------- AST ------------
Query {
    span: Some(
        0..48,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..48,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Subquery {
                    span: Some(
                        14..48,
                    ),
                    lateral: false,
                    subquery: Query {
                        span: Some(
                            26..32,
                        ),
                        with: None,
                        body: SetOperation(
                            SetOperation {
                                span: Some(
                                    26..32,
                                ),
                                op: Except,
                                all: false,
                                left: Select(
                                    SelectStmt {
                                        span: Some(
                                            16..24,
                                        ),
                                        hints: None,
                                        distinct: false,
                                        top_n: None,
                                        select_list: [
                                            StarColumns {
                                                qualified: [
                                                    Star(
                                                        Some(
                                                            23..24,
                                                        ),
                                                    ),
                                                ],
                                                column_filter: None,
                                            },
                                        ],
                                        from: [],
                                        selection: None,
                                        group_by: None,
                                        having: None,
                                        window_list: None,
                                        qualify: None,
                                    },
                                ),
                                right: Select(
                                    SelectStmt {
                                        span: Some(
                                            34..42,
                                        ),
                                        hints: None,
                                        distinct: false,
                                        top_n: None,
                                        select_list: [
                                            StarColumns {
                                                qualified: [
                                                    Star(
                                                        Some(
                                                            41..42,
                                                        ),
                                                    ),
                                                ],
                                                column_filter: None,
                                            },
                                        ],
                                        from: [],
                                        selection: None,
                                        group_by: None,
                                        having: None,
                                        window_list: None,
                                        qualify: None,
                                    },
                                ),
                            },
                        ),
                        order_by: [],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                    alias: Some(
                        TableAlias {
                            name: Identifier {
                                span: Some(
                                    45..48,
                                ),
                                name: "foo",
                                quote: None,
                                ident_type: None,
                            },
                            columns: [],
                        },
                    ),
                    pivot: None,
                    unpivot: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
SELECT * FROM (((SELECT *) EXCEPT (SELECT *))) foo
---------- Output ---------
SELECT * FROM (SELECT * EXCEPT SELECT *) AS foo
---------- AST ------------
Query {
    span: Some(
        0..50,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..50,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Subquery {
                    span: Some(
                        14..50,
                    ),
                    lateral: false,
                    subquery: Query {
                        span: Some(
                            27..33,
                        ),
                        with: None,
                        body: SetOperation(
                            SetOperation {
                                span: Some(
                                    27..33,
                                ),
                                op: Except,
                                all: false,
                                left: Select(
                                    SelectStmt {
                                        span: Some(
                                            17..25,
                                        ),
                                        hints: None,
                                        distinct: false,
                                        top_n: None,
                                        select_list: [
                                            StarColumns {
                                                qualified: [
                                                    Star(
                                                        Some(
                                                            24..25,
                                                        ),
                                                    ),
                                                ],
                                                column_filter: None,
                                            },
                                        ],
                                        from: [],
                                        selection: None,
                                        group_by: None,
                                        having: None,
                                        window_list: None,
                                        qualify: None,
                                    },
                                ),
                                right: Select(
                                    SelectStmt {
                                        span: Some(
                                            35..43,
                                        ),
                                        hints: None,
                                        distinct: false,
                                        top_n: None,
                                        select_list: [
                                            StarColumns {
                                                qualified: [
                                                    Star(
                                                        Some(
                                                            42..43,
                                                        ),
                                                    ),
                                                ],
                                                column_filter: None,
                                            },
                                        ],
                                        from: [],
                                        selection: None,
                                        group_by: None,
                                        having: None,
                                        window_list: None,
                                        qualify: None,
                                    },
                                ),
                            },
                        ),
                        order_by: [],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                    alias: Some(
                        TableAlias {
                            name: Identifier {
                                span: Some(
                                    47..50,
                                ),
                                name: "foo",
                                quote: None,
                                ident_type: None,
                            },
                            columns: [],
                        },
                    ),
                    pivot: None,
                    unpivot: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
SELECT * FROM (SELECT * FROM xyu ORDER BY x, y) AS xyu
---------- Output ---------
SELECT * FROM (SELECT * FROM xyu ORDER BY x, y) AS xyu
---------- AST ------------
Query {
    span: Some(
        0..54,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..54,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Subquery {
                    span: Some(
                        14..54,
                    ),
                    lateral: false,
                    subquery: Query {
                        span: Some(
                            15..32,
                        ),
                        with: None,
                        body: Select(
                            SelectStmt {
                                span: Some(
                                    15..32,
                                ),
                                hints: None,
                                distinct: false,
                                top_n: None,
                                select_list: [
                                    StarColumns {
                                        qualified: [
                                            Star(
                                                Some(
                                                    22..23,
                                                ),
                                            ),
                                        ],
                                        column_filter: None,
                                    },
                                ],
                                from: [
                                    Table {
                                        span: Some(
                                            29..32,
                                        ),
                                        catalog: None,
                                        database: None,
                                        table: Identifier {
                                            span: Some(
                                                29..32,
                                            ),
                                            name: "xyu",
                                            quote: None,
                                            ident_type: None,
                                        },
                                        alias: None,
                                        temporal: None,
                                        with_options: None,
                                        pivot: None,
                                        unpivot: None,
                                        sample: None,
                                    },
                                ],
                                selection: None,
                                group_by: None,
                                having: None,
                                window_list: None,
                                qualify: None,
                            },
                        ),
                        order_by: [
                            OrderByExpr {
                                expr: ColumnRef {
                                    span: Some(
                                        42..43,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    42..43,
                                                ),
                                                name: "x",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                                asc: None,
                                nulls_first: None,
                            },
                            OrderByExpr {
                                expr: ColumnRef {
                                    span: Some(
                                        45..46,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    45..46,
                                                ),
                                                name: "y",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                                asc: None,
                                nulls_first: None,
                            },
                        ],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                    alias: Some(
                        TableAlias {
                            name: Identifier {
                                span: Some(
                                    51..54,
                                ),
                                name: "xyu",
                                quote: None,
                                ident_type: None,
                            },
                            columns: [],
                        },
                    ),
                    pivot: None,
                    unpivot: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from monthly_sales pivot(sum(amount) for month in ('JAN', 'FEB', 'MAR', 'APR')) order by empid
---------- Output ---------
SELECT * FROM monthly_sales PIVOT(sum(amount) FOR month IN ('JAN', 'FEB', 'MAR', 'APR')) ORDER BY empid
---------- AST ------------
Query {
    span: Some(
        0..88,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..88,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        14..88,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            14..27,
                        ),
                        name: "monthly_sales",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: None,
                    with_options: None,
                    pivot: Some(
                        Pivot {
                            aggregate: FunctionCall {
                                span: Some(
                                    34..45,
                                ),
                                func: FunctionCall {
                                    distinct: false,
                                    name: Identifier {
                                        span: Some(
                                            34..37,
                                        ),
                                        name: "sum",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    args: [
                                        ColumnRef {
                                            span: Some(
                                                38..44,
                                            ),
                                            column: ColumnRef {
                                                database: None,
                                                table: None,
                                                column: Name(
                                                    Identifier {
                                                        span: Some(
                                                            38..44,
                                                        ),
                                                        name: "amount",
                                                        quote: None,
                                                        ident_type: None,
                                                    },
                                                ),
                                            },
                                        },
                                    ],
                                    params: [],
                                    order_by: [],
                                    window: None,
                                    lambda: None,
                                },
                            },
                            value_column: Identifier {
                                span: Some(
                                    50..55,
                                ),
                                name: "month",
                                quote: None,
                                ident_type: None,
                            },
                            values: ColumnValues(
                                [
                                    Literal {
                                        span: Some(
                                            60..65,
                                        ),
                                        value: String(
                                            "JAN",
                                        ),
                                    },
                                    Literal {
                                        span: Some(
                                            67..72,
                                        ),
                                        value: String(
                                            "FEB",
                                        ),
                                    },
                                    Literal {
                                        span: Some(
                                            74..79,
                                        ),
                                        value: String(
                                            "MAR",
                                        ),
                                    },
                                    Literal {
                                        span: Some(
                                            81..86,
                                        ),
                                        value: String(
                                            "APR",
                                        ),
                                    },
                                ],
                            ),
                        },
                    ),
                    unpivot: None,
                    sample: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [
        OrderByExpr {
            expr: ColumnRef {
                span: Some(
                    98..103,
                ),
                column: ColumnRef {
                    database: None,
                    table: None,
                    column: Name(
                        Identifier {
                            span: Some(
                                98..103,
                            ),
                            name: "empid",
                            quote: None,
                            ident_type: None,
                        },
                    ),
                },
            },
            asc: None,
            nulls_first: None,
        },
    ],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from (select * from monthly_sales) pivot(sum(amount) for month in ('JAN', 'FEB', 'MAR', 'APR')) order by empid
---------- Output ---------
SELECT * FROM (SELECT * FROM monthly_sales) PIVOT(sum(amount) FOR month IN ('JAN', 'FEB', 'MAR', 'APR')) ORDER BY empid
---------- AST ------------
Query {
    span: Some(
        0..104,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..104,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Subquery {
                    span: Some(
                        14..104,
                    ),
                    lateral: false,
                    subquery: Query {
                        span: Some(
                            15..42,
                        ),
                        with: None,
                        body: Select(
                            SelectStmt {
                                span: Some(
                                    15..42,
                                ),
                                hints: None,
                                distinct: false,
                                top_n: None,
                                select_list: [
                                    StarColumns {
                                        qualified: [
                                            Star(
                                                Some(
                                                    22..23,
                                                ),
                                            ),
                                        ],
                                        column_filter: None,
                                    },
                                ],
                                from: [
                                    Table {
                                        span: Some(
                                            29..42,
                                        ),
                                        catalog: None,
                                        database: None,
                                        table: Identifier {
                                            span: Some(
                                                29..42,
                                            ),
                                            name: "monthly_sales",
                                            quote: None,
                                            ident_type: None,
                                        },
                                        alias: None,
                                        temporal: None,
                                        with_options: None,
                                        pivot: None,
                                        unpivot: None,
                                        sample: None,
                                    },
                                ],
                                selection: None,
                                group_by: None,
                                having: None,
                                window_list: None,
                                qualify: None,
                            },
                        ),
                        order_by: [],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                    alias: None,
                    pivot: Some(
                        Pivot {
                            aggregate: FunctionCall {
                                span: Some(
                                    50..61,
                                ),
                                func: FunctionCall {
                                    distinct: false,
                                    name: Identifier {
                                        span: Some(
                                            50..53,
                                        ),
                                        name: "sum",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    args: [
                                        ColumnRef {
                                            span: Some(
                                                54..60,
                                            ),
                                            column: ColumnRef {
                                                database: None,
                                                table: None,
                                                column: Name(
                                                    Identifier {
                                                        span: Some(
                                                            54..60,
                                                        ),
                                                        name: "amount",
                                                        quote: None,
                                                        ident_type: None,
                                                    },
                                                ),
                                            },
                                        },
                                    ],
                                    params: [],
                                    order_by: [],
                                    window: None,
                                    lambda: None,
                                },
                            },
                            value_column: Identifier {
                                span: Some(
                                    66..71,
                                ),
                                name: "month",
                                quote: None,
                                ident_type: None,
                            },
                            values: ColumnValues(
                                [
                                    Literal {
                                        span: Some(
                                            76..81,
                                        ),
                                        value: String(
                                            "JAN",
                                        ),
                                    },
                                    Literal {
                                        span: Some(
                                            83..88,
                                        ),
                                        value: String(
                                            "FEB",
                                        ),
                                    },
                                    Literal {
                                        span: Some(
                                            90..95,
                                        ),
                                        value: String(
                                            "MAR",
                                        ),
                                    },
                                    Literal {
                                        span: Some(
                                            97..102,
                                        ),
                                        value: String(
                                            "APR",
                                        ),
                                    },
                                ],
                            ),
                        },
                    ),
                    unpivot: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [
        OrderByExpr {
            expr: ColumnRef {
                span: Some(
                    114..119,
                ),
                column: ColumnRef {
                    database: None,
                    table: None,
                    column: Name(
                        Identifier {
                            span: Some(
                                114..119,
                            ),
                            name: "empid",
                            quote: None,
                            ident_type: None,
                        },
                    ),
                },
            },
            asc: None,
            nulls_first: None,
        },
    ],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from monthly_sales pivot(sum(amount) for month in (select distinct month from monthly_sales)) order by empid
---------- Output ---------
SELECT * FROM monthly_sales PIVOT(sum(amount) FOR month IN (SELECT DISTINCT month FROM monthly_sales)) ORDER BY empid
---------- AST ------------
Query {
    span: Some(
        0..102,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..102,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        14..102,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            14..27,
                        ),
                        name: "monthly_sales",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: None,
                    with_options: None,
                    pivot: Some(
                        Pivot {
                            aggregate: FunctionCall {
                                span: Some(
                                    34..45,
                                ),
                                func: FunctionCall {
                                    distinct: false,
                                    name: Identifier {
                                        span: Some(
                                            34..37,
                                        ),
                                        name: "sum",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    args: [
                                        ColumnRef {
                                            span: Some(
                                                38..44,
                                            ),
                                            column: ColumnRef {
                                                database: None,
                                                table: None,
                                                column: Name(
                                                    Identifier {
                                                        span: Some(
                                                            38..44,
                                                        ),
                                                        name: "amount",
                                                        quote: None,
                                                        ident_type: None,
                                                    },
                                                ),
                                            },
                                        },
                                    ],
                                    params: [],
                                    order_by: [],
                                    window: None,
                                    lambda: None,
                                },
                            },
                            value_column: Identifier {
                                span: Some(
                                    50..55,
                                ),
                                name: "month",
                                quote: None,
                                ident_type: None,
                            },
                            values: Subquery(
                                Query {
                                    span: Some(
                                        60..100,
                                    ),
                                    with: None,
                                    body: Select(
                                        SelectStmt {
                                            span: Some(
                                                60..100,
                                            ),
                                            hints: None,
                                            distinct: true,
                                            top_n: None,
                                            select_list: [
                                                AliasedExpr {
                                                    expr: ColumnRef {
                                                        span: Some(
                                                            76..81,
                                                        ),
                                                        column: ColumnRef {
                                                            database: None,
                                                            table: None,
                                                            column: Name(
                                                                Identifier {
                                                                    span: Some(
                                                                        76..81,
                                                                    ),
                                                                    name: "month",
                                                                    quote: None,
                                                                    ident_type: None,
                                                                },
                                                            ),
                                                        },
                                                    },
                                                    alias: None,
                                                },
                                            ],
                                            from: [
                                                Table {
                                                    span: Some(
                                                        87..100,
                                                    ),
                                                    catalog: None,
                                                    database: None,
                                                    table: Identifier {
                                                        span: Some(
                                                            87..100,
                                                        ),
                                                        name: "monthly_sales",
                                                        quote: None,
                                                        ident_type: None,
                                                    },
                                                    alias: None,
                                                    temporal: None,
                                                    with_options: None,
                                                    pivot: None,
                                                    unpivot: None,
                                                    sample: None,
                                                },
                                            ],
                                            selection: None,
                                            group_by: None,
                                            having: None,
                                            window_list: None,
                                            qualify: None,
                                        },
                                    ),
                                    order_by: [],
                                    limit: [],
                                    offset: None,
                                    ignore_result: false,
                                },
                            ),
                        },
                    ),
                    unpivot: None,
                    sample: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [
        OrderByExpr {
            expr: ColumnRef {
                span: Some(
                    112..117,
                ),
                column: ColumnRef {
                    database: None,
                    table: None,
                    column: Name(
                        Identifier {
                            span: Some(
                                112..117,
                            ),
                            name: "empid",
                            quote: None,
                            ident_type: None,
                        },
                    ),
                },
            },
            asc: None,
            nulls_first: None,
        },
    ],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from (select * from monthly_sales) pivot(sum(amount) for month in ((select distinct month from monthly_sales))) order by empid
---------- Output ---------
SELECT * FROM (SELECT * FROM monthly_sales) PIVOT(sum(amount) FOR month IN ((SELECT DISTINCT month FROM monthly_sales))) ORDER BY empid
---------- AST ------------
Query {
    span: Some(
        0..120,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..120,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Subquery {
                    span: Some(
                        14..120,
                    ),
                    lateral: false,
                    subquery: Query {
                        span: Some(
                            15..42,
                        ),
                        with: None,
                        body: Select(
                            SelectStmt {
                                span: Some(
                                    15..42,
                                ),
                                hints: None,
                                distinct: false,
                                top_n: None,
                                select_list: [
                                    StarColumns {
                                        qualified: [
                                            Star(
                                                Some(
                                                    22..23,
                                                ),
                                            ),
                                        ],
                                        column_filter: None,
                                    },
                                ],
                                from: [
                                    Table {
                                        span: Some(
                                            29..42,
                                        ),
                                        catalog: None,
                                        database: None,
                                        table: Identifier {
                                            span: Some(
                                                29..42,
                                            ),
                                            name: "monthly_sales",
                                            quote: None,
                                            ident_type: None,
                                        },
                                        alias: None,
                                        temporal: None,
                                        with_options: None,
                                        pivot: None,
                                        unpivot: None,
                                        sample: None,
                                    },
                                ],
                                selection: None,
                                group_by: None,
                                having: None,
                                window_list: None,
                                qualify: None,
                            },
                        ),
                        order_by: [],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                    alias: None,
                    pivot: Some(
                        Pivot {
                            aggregate: FunctionCall {
                                span: Some(
                                    50..61,
                                ),
                                func: FunctionCall {
                                    distinct: false,
                                    name: Identifier {
                                        span: Some(
                                            50..53,
                                        ),
                                        name: "sum",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    args: [
                                        ColumnRef {
                                            span: Some(
                                                54..60,
                                            ),
                                            column: ColumnRef {
                                                database: None,
                                                table: None,
                                                column: Name(
                                                    Identifier {
                                                        span: Some(
                                                            54..60,
                                                        ),
                                                        name: "amount",
                                                        quote: None,
                                                        ident_type: None,
                                                    },
                                                ),
                                            },
                                        },
                                    ],
                                    params: [],
                                    order_by: [],
                                    window: None,
                                    lambda: None,
                                },
                            },
                            value_column: Identifier {
                                span: Some(
                                    66..71,
                                ),
                                name: "month",
                                quote: None,
                                ident_type: None,
                            },
                            values: ColumnValues(
                                [
                                    Subquery {
                                        span: Some(
                                            76..118,
                                        ),
                                        modifier: None,
                                        subquery: Query {
                                            span: Some(
                                                77..117,
                                            ),
                                            with: None,
                                            body: Select(
                                                SelectStmt {
                                                    span: Some(
                                                        77..117,
                                                    ),
                                                    hints: None,
                                                    distinct: true,
                                                    top_n: None,
                                                    select_list: [
                                                        AliasedExpr {
                                                            expr: ColumnRef {
                                                                span: Some(
                                                                    93..98,
                                                                ),
                                                                column: ColumnRef {
                                                                    database: None,
                                                                    table: None,
                                                                    column: Name(
                                                                        Identifier {
                                                                            span: Some(
                                                                                93..98,
                                                                            ),
                                                                            name: "month",
                                                                            quote: None,
                                                                            ident_type: None,
                                                                        },
                                                                    ),
                                                                },
                                                            },
                                                            alias: None,
                                                        },
                                                    ],
                                                    from: [
                                                        Table {
                                                            span: Some(
                                                                104..117,
                                                            ),
                                                            catalog: None,
                                                            database: None,
                                                            table: Identifier {
                                                                span: Some(
                                                                    104..117,
                                                                ),
                                                                name: "monthly_sales",
                                                                quote: None,
                                                                ident_type: None,
                                                            },
                                                            alias: None,
                                                            temporal: None,
                                                            with_options: None,
                                                            pivot: None,
                                                            unpivot: None,
                                                            sample: None,
                                                        },
                                                    ],
                                                    selection: None,
                                                    group_by: None,
                                                    having: None,
                                                    window_list: None,
                                                    qualify: None,
                                                },
                                            ),
                                            order_by: [],
                                            limit: [],
                                            offset: None,
                                            ignore_result: false,
                                        },
                                    },
                                ],
                            ),
                        },
                    ),
                    unpivot: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [
        OrderByExpr {
            expr: ColumnRef {
                span: Some(
                    130..135,
                ),
                column: ColumnRef {
                    database: None,
                    table: None,
                    column: Name(
                        Identifier {
                            span: Some(
                                130..135,
                            ),
                            name: "empid",
                            quote: None,
                            ident_type: None,
                        },
                    ),
                },
            },
            asc: None,
            nulls_first: None,
        },
    ],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from monthly_sales_1 unpivot(sales for month in (jan as '1月', feb 'February', mar As 'MARCH', april)) order by empid
---------- Output ---------
SELECT * FROM monthly_sales_1 UNPIVOT(sales FOR month IN (jan AS '1月', feb AS 'February', mar AS 'MARCH', april)) ORDER BY empid
---------- AST ------------
Query {
    span: Some(
        0..112,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..112,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        14..112,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            14..29,
                        ),
                        name: "monthly_sales_1",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: None,
                    with_options: None,
                    pivot: None,
                    unpivot: Some(
                        Unpivot {
                            value_column: Identifier {
                                span: Some(
                                    38..43,
                                ),
                                name: "sales",
                                quote: None,
                                ident_type: None,
                            },
                            unpivot_column: Identifier {
                                span: Some(
                                    48..53,
                                ),
                                name: "month",
                                quote: None,
                                ident_type: None,
                            },
                            column_names: [
                                UnpivotName {
                                    ident: Identifier {
                                        span: Some(
                                            58..61,
                                        ),
                                        name: "jan",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: Some(
                                        "1月",
                                    ),
                                },
                                UnpivotName {
                                    ident: Identifier {
                                        span: Some(
                                            73..76,
                                        ),
                                        name: "feb",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: Some(
                                        "February",
                                    ),
                                },
                                UnpivotName {
                                    ident: Identifier {
                                        span: Some(
                                            89..92,
                                        ),
                                        name: "mar",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: Some(
                                        "MARCH",
                                    ),
                                },
                                UnpivotName {
                                    ident: Identifier {
                                        span: Some(
                                            105..110,
                                        ),
                                        name: "april",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                },
                            ],
                        },
                    ),
                    sample: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [
        OrderByExpr {
            expr: ColumnRef {
                span: Some(
                    122..127,
                ),
                column: ColumnRef {
                    database: None,
                    table: None,
                    column: Name(
                        Identifier {
                            span: Some(
                                122..127,
                            ),
                            name: "empid",
                            quote: None,
                            ident_type: None,
                        },
                    ),
                },
            },
            asc: None,
            nulls_first: None,
        },
    ],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from (select * from monthly_sales_1) unpivot(sales for month in (jan, feb, mar, april)) order by empid
---------- Output ---------
SELECT * FROM (SELECT * FROM monthly_sales_1) UNPIVOT(sales FOR month IN (jan, feb, mar, april)) ORDER BY empid
---------- AST ------------
Query {
    span: Some(
        0..96,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..96,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Subquery {
                    span: Some(
                        14..96,
                    ),
                    lateral: false,
                    subquery: Query {
                        span: Some(
                            15..44,
                        ),
                        with: None,
                        body: Select(
                            SelectStmt {
                                span: Some(
                                    15..44,
                                ),
                                hints: None,
                                distinct: false,
                                top_n: None,
                                select_list: [
                                    StarColumns {
                                        qualified: [
                                            Star(
                                                Some(
                                                    22..23,
                                                ),
                                            ),
                                        ],
                                        column_filter: None,
                                    },
                                ],
                                from: [
                                    Table {
                                        span: Some(
                                            29..44,
                                        ),
                                        catalog: None,
                                        database: None,
                                        table: Identifier {
                                            span: Some(
                                                29..44,
                                            ),
                                            name: "monthly_sales_1",
                                            quote: None,
                                            ident_type: None,
                                        },
                                        alias: None,
                                        temporal: None,
                                        with_options: None,
                                        pivot: None,
                                        unpivot: None,
                                        sample: None,
                                    },
                                ],
                                selection: None,
                                group_by: None,
                                having: None,
                                window_list: None,
                                qualify: None,
                            },
                        ),
                        order_by: [],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                    alias: None,
                    pivot: None,
                    unpivot: Some(
                        Unpivot {
                            value_column: Identifier {
                                span: Some(
                                    54..59,
                                ),
                                name: "sales",
                                quote: None,
                                ident_type: None,
                            },
                            unpivot_column: Identifier {
                                span: Some(
                                    64..69,
                                ),
                                name: "month",
                                quote: None,
                                ident_type: None,
                            },
                            column_names: [
                                UnpivotName {
                                    ident: Identifier {
                                        span: Some(
                                            74..77,
                                        ),
                                        name: "jan",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                },
                                UnpivotName {
                                    ident: Identifier {
                                        span: Some(
                                            79..82,
                                        ),
                                        name: "feb",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                },
                                UnpivotName {
                                    ident: Identifier {
                                        span: Some(
                                            84..87,
                                        ),
                                        name: "mar",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                },
                                UnpivotName {
                                    ident: Identifier {
                                        span: Some(
                                            89..94,
                                        ),
                                        name: "april",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    alias: None,
                                },
                            ],
                        },
                    ),
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [
        OrderByExpr {
            expr: ColumnRef {
                span: Some(
                    106..111,
                ),
                column: ColumnRef {
                    database: None,
                    table: None,
                    column: Name(
                        Identifier {
                            span: Some(
                                106..111,
                            ),
                            name: "empid",
                            quote: None,
                            ident_type: None,
                        },
                    ),
                },
            },
            asc: None,
            nulls_first: None,
        },
    ],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from range(1, 2)
---------- Output ---------
SELECT * FROM range(1, 2)
---------- AST ------------
Query {
    span: Some(
        0..25,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..25,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                TableFunction {
                    span: Some(
                        14..25,
                    ),
                    lateral: false,
                    name: Identifier {
                        span: Some(
                            14..19,
                        ),
                        name: "range",
                        quote: None,
                        ident_type: None,
                    },
                    params: [
                        Literal {
                            span: Some(
                                20..21,
                            ),
                            value: UInt64(
                                1,
                            ),
                        },
                        Literal {
                            span: Some(
                                23..24,
                            ),
                            value: UInt64(
                                2,
                            ),
                        },
                    ],
                    named_params: [],
                    alias: None,
                    sample: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select sum(a) over w from customer window w as (partition by a order by b)
---------- Output ---------
SELECT sum(a) OVER w FROM customer WINDOW w AS (PARTITION BY a ORDER BY b)
---------- AST ------------
Query {
    span: Some(
        0..74,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..74,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                AliasedExpr {
                    expr: FunctionCall {
                        span: Some(
                            7..20,
                        ),
                        func: FunctionCall {
                            distinct: false,
                            name: Identifier {
                                span: Some(
                                    7..10,
                                ),
                                name: "sum",
                                quote: None,
                                ident_type: None,
                            },
                            args: [
                                ColumnRef {
                                    span: Some(
                                        11..12,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    11..12,
                                                ),
                                                name: "a",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                            ],
                            params: [],
                            order_by: [],
                            window: Some(
                                WindowDesc {
                                    ignore_nulls: None,
                                    window: WindowReference(
                                        WindowRef {
                                            window_name: Identifier {
                                                span: Some(
                                                    19..20,
                                                ),
                                                name: "w",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        },
                                    ),
                                },
                            ),
                            lambda: None,
                        },
                    },
                    alias: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        26..34,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            26..34,
                        ),
                        name: "customer",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: None,
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: Some(
                [
                    WindowDefinition {
                        name: Identifier {
                            span: Some(
                                42..43,
                            ),
                            name: "w",
                            quote: None,
                            ident_type: None,
                        },
                        spec: WindowSpec {
                            existing_window_name: None,
                            partition_by: [
                                ColumnRef {
                                    span: Some(
                                        61..62,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    61..62,
                                                ),
                                                name: "a",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                            ],
                            order_by: [
                                OrderByExpr {
                                    expr: ColumnRef {
                                        span: Some(
                                            72..73,
                                        ),
                                        column: ColumnRef {
                                            database: None,
                                            table: None,
                                            column: Name(
                                                Identifier {
                                                    span: Some(
                                                        72..73,
                                                    ),
                                                    name: "b",
                                                    quote: None,
                                                    ident_type: None,
                                                },
                                            ),
                                        },
                                    },
                                    asc: None,
                                    nulls_first: None,
                                },
                            ],
                            window_frame: None,
                        },
                    },
                ],
            ),
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select a, sum(a) over w, sum(a) over w1, sum(a) over w2 from t1 window w as (partition by a), w2 as (w1 rows current row), w1 as (w order by a) order by a
---------- Output ---------
SELECT a, sum(a) OVER w, sum(a) OVER w1, sum(a) OVER w2 FROM t1 WINDOW w AS (PARTITION BY a), w2 AS (w1 ROWS BETWEEN CURRENT ROW AND CURRENT ROW), w1 AS (w ORDER BY a) ORDER BY a
---------- AST ------------
Query {
    span: Some(
        0..143,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..143,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                AliasedExpr {
                    expr: ColumnRef {
                        span: Some(
                            7..8,
                        ),
                        column: ColumnRef {
                            database: None,
                            table: None,
                            column: Name(
                                Identifier {
                                    span: Some(
                                        7..8,
                                    ),
                                    name: "a",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                        },
                    },
                    alias: None,
                },
                AliasedExpr {
                    expr: FunctionCall {
                        span: Some(
                            10..23,
                        ),
                        func: FunctionCall {
                            distinct: false,
                            name: Identifier {
                                span: Some(
                                    10..13,
                                ),
                                name: "sum",
                                quote: None,
                                ident_type: None,
                            },
                            args: [
                                ColumnRef {
                                    span: Some(
                                        14..15,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    14..15,
                                                ),
                                                name: "a",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                            ],
                            params: [],
                            order_by: [],
                            window: Some(
                                WindowDesc {
                                    ignore_nulls: None,
                                    window: WindowReference(
                                        WindowRef {
                                            window_name: Identifier {
                                                span: Some(
                                                    22..23,
                                                ),
                                                name: "w",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        },
                                    ),
                                },
                            ),
                            lambda: None,
                        },
                    },
                    alias: None,
                },
                AliasedExpr {
                    expr: FunctionCall {
                        span: Some(
                            25..39,
                        ),
                        func: FunctionCall {
                            distinct: false,
                            name: Identifier {
                                span: Some(
                                    25..28,
                                ),
                                name: "sum",
                                quote: None,
                                ident_type: None,
                            },
                            args: [
                                ColumnRef {
                                    span: Some(
                                        29..30,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    29..30,
                                                ),
                                                name: "a",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                            ],
                            params: [],
                            order_by: [],
                            window: Some(
                                WindowDesc {
                                    ignore_nulls: None,
                                    window: WindowReference(
                                        WindowRef {
                                            window_name: Identifier {
                                                span: Some(
                                                    37..39,
                                                ),
                                                name: "w1",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        },
                                    ),
                                },
                            ),
                            lambda: None,
                        },
                    },
                    alias: None,
                },
                AliasedExpr {
                    expr: FunctionCall {
                        span: Some(
                            41..55,
                        ),
                        func: FunctionCall {
                            distinct: false,
                            name: Identifier {
                                span: Some(
                                    41..44,
                                ),
                                name: "sum",
                                quote: None,
                                ident_type: None,
                            },
                            args: [
                                ColumnRef {
                                    span: Some(
                                        45..46,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    45..46,
                                                ),
                                                name: "a",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                            ],
                            params: [],
                            order_by: [],
                            window: Some(
                                WindowDesc {
                                    ignore_nulls: None,
                                    window: WindowReference(
                                        WindowRef {
                                            window_name: Identifier {
                                                span: Some(
                                                    53..55,
                                                ),
                                                name: "w2",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        },
                                    ),
                                },
                            ),
                            lambda: None,
                        },
                    },
                    alias: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        61..63,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            61..63,
                        ),
                        name: "t1",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: None,
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: Some(
                [
                    WindowDefinition {
                        name: Identifier {
                            span: Some(
                                71..72,
                            ),
                            name: "w",
                            quote: None,
                            ident_type: None,
                        },
                        spec: WindowSpec {
                            existing_window_name: None,
                            partition_by: [
                                ColumnRef {
                                    span: Some(
                                        90..91,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    90..91,
                                                ),
                                                name: "a",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                            ],
                            order_by: [],
                            window_frame: None,
                        },
                    },
                    WindowDefinition {
                        name: Identifier {
                            span: Some(
                                94..96,
                            ),
                            name: "w2",
                            quote: None,
                            ident_type: None,
                        },
                        spec: WindowSpec {
                            existing_window_name: Some(
                                Identifier {
                                    span: Some(
                                        101..103,
                                    ),
                                    name: "w1",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                            partition_by: [],
                            order_by: [],
                            window_frame: Some(
                                WindowFrame {
                                    units: Rows,
                                    start_bound: CurrentRow,
                                    end_bound: CurrentRow,
                                },
                            ),
                        },
                    },
                    WindowDefinition {
                        name: Identifier {
                            span: Some(
                                123..125,
                            ),
                            name: "w1",
                            quote: None,
                            ident_type: None,
                        },
                        spec: WindowSpec {
                            existing_window_name: Some(
                                Identifier {
                                    span: Some(
                                        130..131,
                                    ),
                                    name: "w",
                                    quote: None,
                                    ident_type: None,
                                },
                            ),
                            partition_by: [],
                            order_by: [
                                OrderByExpr {
                                    expr: ColumnRef {
                                        span: Some(
                                            141..142,
                                        ),
                                        column: ColumnRef {
                                            database: None,
                                            table: None,
                                            column: Name(
                                                Identifier {
                                                    span: Some(
                                                        141..142,
                                                    ),
                                                    name: "a",
                                                    quote: None,
                                                    ident_type: None,
                                                },
                                            ),
                                        },
                                    },
                                    asc: None,
                                    nulls_first: None,
                                },
                            ],
                            window_frame: None,
                        },
                    },
                ],
            ),
            qualify: None,
        },
    ),
    order_by: [
        OrderByExpr {
            expr: ColumnRef {
                span: Some(
                    153..154,
                ),
                column: ColumnRef {
                    database: None,
                    table: None,
                    column: Name(
                        Identifier {
                            span: Some(
                                153..154,
                            ),
                            name: "a",
                            quote: None,
                            ident_type: None,
                        },
                    ),
                },
            },
            asc: None,
            nulls_first: None,
        },
    ],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
SELECT * FROM ((SELECT * FROM xyu ORDER BY x, y)) AS xyu
---------- Output ---------
SELECT * FROM (SELECT * FROM xyu ORDER BY x, y) AS xyu
---------- AST ------------
Query {
    span: Some(
        0..56,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..56,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Subquery {
                    span: Some(
                        14..56,
                    ),
                    lateral: false,
                    subquery: Query {
                        span: Some(
                            16..33,
                        ),
                        with: None,
                        body: Select(
                            SelectStmt {
                                span: Some(
                                    16..33,
                                ),
                                hints: None,
                                distinct: false,
                                top_n: None,
                                select_list: [
                                    StarColumns {
                                        qualified: [
                                            Star(
                                                Some(
                                                    23..24,
                                                ),
                                            ),
                                        ],
                                        column_filter: None,
                                    },
                                ],
                                from: [
                                    Table {
                                        span: Some(
                                            30..33,
                                        ),
                                        catalog: None,
                                        database: None,
                                        table: Identifier {
                                            span: Some(
                                                30..33,
                                            ),
                                            name: "xyu",
                                            quote: None,
                                            ident_type: None,
                                        },
                                        alias: None,
                                        temporal: None,
                                        with_options: None,
                                        pivot: None,
                                        unpivot: None,
                                        sample: None,
                                    },
                                ],
                                selection: None,
                                group_by: None,
                                having: None,
                                window_list: None,
                                qualify: None,
                            },
                        ),
                        order_by: [
                            OrderByExpr {
                                expr: ColumnRef {
                                    span: Some(
                                        43..44,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    43..44,
                                                ),
                                                name: "x",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                                asc: None,
                                nulls_first: None,
                            },
                            OrderByExpr {
                                expr: ColumnRef {
                                    span: Some(
                                        46..47,
                                    ),
                                    column: ColumnRef {
                                        database: None,
                                        table: None,
                                        column: Name(
                                            Identifier {
                                                span: Some(
                                                    46..47,
                                                ),
                                                name: "y",
                                                quote: None,
                                                ident_type: None,
                                            },
                                        ),
                                    },
                                },
                                asc: None,
                                nulls_first: None,
                            },
                        ],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                    alias: Some(
                        TableAlias {
                            name: Identifier {
                                span: Some(
                                    53..56,
                                ),
                                name: "xyu",
                                quote: None,
                                ident_type: None,
                            },
                            columns: [],
                        },
                    ),
                    pivot: None,
                    unpivot: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
SELECT * FROM (VALUES(1,1),(2,null),(null,5)) AS t(a,b)
---------- Output ---------
SELECT * FROM (VALUES(1, 1), (2, NULL), (NULL, 5)) AS t(a, b)
---------- AST ------------
Query {
    span: Some(
        0..55,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..55,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Subquery {
                    span: Some(
                        14..55,
                    ),
                    lateral: false,
                    subquery: Query {
                        span: Some(
                            15..44,
                        ),
                        with: None,
                        body: Values {
                            span: Some(
                                15..44,
                            ),
                            values: [
                                [
                                    Literal {
                                        span: Some(
                                            22..23,
                                        ),
                                        value: UInt64(
                                            1,
                                        ),
                                    },
                                    Literal {
                                        span: Some(
                                            24..25,
                                        ),
                                        value: UInt64(
                                            1,
                                        ),
                                    },
                                ],
                                [
                                    Literal {
                                        span: Some(
                                            28..29,
                                        ),
                                        value: UInt64(
                                            2,
                                        ),
                                    },
                                    Literal {
                                        span: Some(
                                            30..34,
                                        ),
                                        value: Null,
                                    },
                                ],
                                [
                                    Literal {
                                        span: Some(
                                            37..41,
                                        ),
                                        value: Null,
                                    },
                                    Literal {
                                        span: Some(
                                            42..43,
                                        ),
                                        value: UInt64(
                                            5,
                                        ),
                                    },
                                ],
                            ],
                        },
                        order_by: [],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                    alias: Some(
                        TableAlias {
                            name: Identifier {
                                span: Some(
                                    49..50,
                                ),
                                name: "t",
                                quote: None,
                                ident_type: None,
                            },
                            columns: [
                                Identifier {
                                    span: Some(
                                        51..52,
                                    ),
                                    name: "a",
                                    quote: None,
                                    ident_type: None,
                                },
                                Identifier {
                                    span: Some(
                                        53..54,
                                    ),
                                    name: "b",
                                    quote: None,
                                    ident_type: None,
                                },
                            ],
                        },
                    ),
                    pivot: None,
                    unpivot: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
VALUES(1,'a'),(2,'b'),(null,'c') order by col0 limit 2
---------- Output ---------
VALUES(1, 'a'), (2, 'b'), (NULL, 'c') ORDER BY col0 LIMIT 2
---------- AST ------------
Query {
    span: Some(
        0..32,
    ),
    with: None,
    body: Values {
        span: Some(
            0..32,
        ),
        values: [
            [
                Literal {
                    span: Some(
                        7..8,
                    ),
                    value: UInt64(
                        1,
                    ),
                },
                Literal {
                    span: Some(
                        9..12,
                    ),
                    value: String(
                        "a",
                    ),
                },
            ],
            [
                Literal {
                    span: Some(
                        15..16,
                    ),
                    value: UInt64(
                        2,
                    ),
                },
                Literal {
                    span: Some(
                        17..20,
                    ),
                    value: String(
                        "b",
                    ),
                },
            ],
            [
                Literal {
                    span: Some(
                        23..27,
                    ),
                    value: Null,
                },
                Literal {
                    span: Some(
                        28..31,
                    ),
                    value: String(
                        "c",
                    ),
                },
            ],
        ],
    },
    order_by: [
        OrderByExpr {
            expr: ColumnRef {
                span: Some(
                    42..46,
                ),
                column: ColumnRef {
                    database: None,
                    table: None,
                    column: Name(
                        Identifier {
                            span: Some(
                                42..46,
                            ),
                            name: "col0",
                            quote: None,
                            ident_type: None,
                        },
                    ),
                },
            },
            asc: None,
            nulls_first: None,
        },
    ],
    limit: [
        Literal {
            span: Some(
                53..54,
            ),
            value: UInt64(
                2,
            ),
        },
    ],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from t left join lateral(select 1) on true, lateral(select 2)
---------- Output ---------
SELECT * FROM t LEFT OUTER JOIN LATERAL (SELECT 1) ON TRUE, LATERAL (SELECT 2)
---------- AST ------------
Query {
    span: Some(
        0..70,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..70,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Join {
                    span: Some(
                        16..25,
                    ),
                    join: Join {
                        op: LeftOuter,
                        condition: On(
                            Literal {
                                span: Some(
                                    47..51,
                                ),
                                value: Boolean(
                                    true,
                                ),
                            },
                        ),
                        left: Table {
                            span: Some(
                                14..15,
                            ),
                            catalog: None,
                            database: None,
                            table: Identifier {
                                span: Some(
                                    14..15,
                                ),
                                name: "t",
                                quote: None,
                                ident_type: None,
                            },
                            alias: None,
                            temporal: None,
                            with_options: None,
                            pivot: None,
                            unpivot: None,
                            sample: None,
                        },
                        right: Subquery {
                            span: Some(
                                26..43,
                            ),
                            lateral: true,
                            subquery: Query {
                                span: Some(
                                    34..42,
                                ),
                                with: None,
                                body: Select(
                                    SelectStmt {
                                        span: Some(
                                            34..42,
                                        ),
                                        hints: None,
                                        distinct: false,
                                        top_n: None,
                                        select_list: [
                                            AliasedExpr {
                                                expr: Literal {
                                                    span: Some(
                                                        41..42,
                                                    ),
                                                    value: UInt64(
                                                        1,
                                                    ),
                                                },
                                                alias: None,
                                            },
                                        ],
                                        from: [],
                                        selection: None,
                                        group_by: None,
                                        having: None,
                                        window_list: None,
                                        qualify: None,
                                    },
                                ),
                                order_by: [],
                                limit: [],
                                offset: None,
                                ignore_result: false,
                            },
                            alias: None,
                            pivot: None,
                            unpivot: None,
                        },
                    },
                },
                Subquery {
                    span: Some(
                        53..70,
                    ),
                    lateral: true,
                    subquery: Query {
                        span: Some(
                            61..69,
                        ),
                        with: None,
                        body: Select(
                            SelectStmt {
                                span: Some(
                                    61..69,
                                ),
                                hints: None,
                                distinct: false,
                                top_n: None,
                                select_list: [
                                    AliasedExpr {
                                        expr: Literal {
                                            span: Some(
                                                68..69,
                                            ),
                                            value: UInt64(
                                                2,
                                            ),
                                        },
                                        alias: None,
                                    },
                                ],
                                from: [],
                                selection: None,
                                group_by: None,
                                having: None,
                                window_list: None,
                                qualify: None,
                            },
                        ),
                        order_by: [],
                        limit: [],
                        offset: None,
                        ignore_result: false,
                    },
                    alias: None,
                    pivot: None,
                    unpivot: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from t, lateral flatten(input => u.col) f
---------- Output ---------
SELECT * FROM t, LATERAL flatten(input=>u.col) AS f
---------- AST ------------
Query {
    span: Some(
        0..50,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..50,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                Table {
                    span: Some(
                        14..15,
                    ),
                    catalog: None,
                    database: None,
                    table: Identifier {
                        span: Some(
                            14..15,
                        ),
                        name: "t",
                        quote: None,
                        ident_type: None,
                    },
                    alias: None,
                    temporal: None,
                    with_options: None,
                    pivot: None,
                    unpivot: None,
                    sample: None,
                },
                TableFunction {
                    span: Some(
                        17..50,
                    ),
                    lateral: true,
                    name: Identifier {
                        span: Some(
                            25..32,
                        ),
                        name: "flatten",
                        quote: None,
                        ident_type: None,
                    },
                    params: [],
                    named_params: [
                        (
                            Identifier {
                                span: Some(
                                    33..38,
                                ),
                                name: "input",
                                quote: None,
                                ident_type: None,
                            },
                            ColumnRef {
                                span: Some(
                                    42..43,
                                ),
                                column: ColumnRef {
                                    database: None,
                                    table: Some(
                                        Identifier {
                                            span: Some(
                                                42..43,
                                            ),
                                            name: "u",
                                            quote: None,
                                            ident_type: None,
                                        },
                                    ),
                                    column: Name(
                                        Identifier {
                                            span: Some(
                                                44..47,
                                            ),
                                            name: "col",
                                            quote: None,
                                            ident_type: None,
                                        },
                                    ),
                                },
                            },
                        ),
                    ],
                    alias: Some(
                        TableAlias {
                            name: Identifier {
                                span: Some(
                                    49..50,
                                ),
                                name: "f",
                                quote: None,
                                ident_type: None,
                            },
                            columns: [],
                        },
                    ),
                    sample: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


---------- Input ----------
select * from flatten(input => parse_json('{"a":1, "b":[77,88]}'), outer => true)
---------- Output ---------
SELECT * FROM flatten(input=>parse_json('{"a":1, "b":[77,88]}'),outer=>TRUE)
---------- AST ------------
Query {
    span: Some(
        0..81,
    ),
    with: None,
    body: Select(
        SelectStmt {
            span: Some(
                0..81,
            ),
            hints: None,
            distinct: false,
            top_n: None,
            select_list: [
                StarColumns {
                    qualified: [
                        Star(
                            Some(
                                7..8,
                            ),
                        ),
                    ],
                    column_filter: None,
                },
            ],
            from: [
                TableFunction {
                    span: Some(
                        14..81,
                    ),
                    lateral: false,
                    name: Identifier {
                        span: Some(
                            14..21,
                        ),
                        name: "flatten",
                        quote: None,
                        ident_type: None,
                    },
                    params: [],
                    named_params: [
                        (
                            Identifier {
                                span: Some(
                                    22..27,
                                ),
                                name: "input",
                                quote: None,
                                ident_type: None,
                            },
                            FunctionCall {
                                span: Some(
                                    31..65,
                                ),
                                func: FunctionCall {
                                    distinct: false,
                                    name: Identifier {
                                        span: Some(
                                            31..41,
                                        ),
                                        name: "parse_json",
                                        quote: None,
                                        ident_type: None,
                                    },
                                    args: [
                                        Literal {
                                            span: Some(
                                                42..64,
                                            ),
                                            value: String(
                                                "{\"a\":1, \"b\":[77,88]}",
                                            ),
                                        },
                                    ],
                                    params: [],
                                    order_by: [],
                                    window: None,
                                    lambda: None,
                                },
                            },
                        ),
                        (
                            Identifier {
                                span: Some(
                                    67..72,
                                ),
                                name: "outer",
                                quote: None,
                                ident_type: None,
                            },
                            Literal {
                                span: Some(
                                    76..80,
                                ),
                                value: Boolean(
                                    true,
                                ),
                            },
                        ),
                    ],
                    alias: None,
                    sample: None,
                },
            ],
            selection: None,
            group_by: None,
            having: None,
            window_list: None,
            qualify: None,
        },
    ),
    order_by: [],
    limit: [],
    offset: None,
    ignore_result: false,
}


