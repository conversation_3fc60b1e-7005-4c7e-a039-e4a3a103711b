ast            : upper('Abc')
raw expr       : upper('Abc')
checked expr   : upper<String>("Abc")
optimized expr : "ABC"
output type    : String
output domain  : {"ABC"..="ABC"}
output         : 'ABC'


ast            : upper('Dobrý den')
raw expr       : upper('Dobrý den')
checked expr   : upper<String>("Dobrý den")
optimized expr : "DOBRÝ DEN"
output type    : String
output domain  : {"DOBRÝ DEN"..="DOBRÝ DEN"}
output         : 'DOBRÝ DEN'


ast            : upper('ß😀山')
raw expr       : upper('ß😀山')
checked expr   : upper<String>("ß😀山")
optimized expr : "SS😀山"
output type    : String
output domain  : {"SS😀山"..="SS😀山"}
output         : 'SS😀山'


ast            : upper(NULL)
raw expr       : upper(NULL)
checked expr   : upper<String NULL>(CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : ucase(a)
raw expr       : ucase(a::String)
checked expr   : upper<String>(a)
evaluation:
+--------+-------------------+-------------+
|        | a                 | Output      |
+--------+-------------------+-------------+
| Type   | String            | String      |
| Domain | {"Abc"..="ß😀山"} | {""..}      |
| Row 0  | 'Abc'             | 'ABC'       |
| Row 1  | 'Dobrý den'       | 'DOBRÝ DEN' |
| Row 2  | 'ß😀山'           | 'SS😀山'    |
+--------+-------------------+-------------+
evaluation (internal):
+--------+--------------------------------------+
| Column | Data                                 |
+--------+--------------------------------------+
| a      | StringColumn[Abc, Dobrý den, ß😀山]  |
| Output | StringColumn[ABC, DOBRÝ DEN, SS😀山] |
+--------+--------------------------------------+


ast            : lower('Abc')
raw expr       : lower('Abc')
checked expr   : lower<String>("Abc")
optimized expr : "abc"
output type    : String
output domain  : {"abc"..="abc"}
output         : 'abc'


ast            : lower('DOBRÝ DEN')
raw expr       : lower('DOBRÝ DEN')
checked expr   : lower<String>("DOBRÝ DEN")
optimized expr : "dobrý den"
output type    : String
output domain  : {"dobrý den"..="dobrý den"}
output         : 'dobrý den'


ast            : lower('İ😀山')
raw expr       : lower('İ😀山')
checked expr   : lower<String>("İ😀山")
optimized expr : "i\u{307}😀山"
output type    : String
output domain  : {"i\u{307}😀山"..="i\u{307}😀山"}
output         : 'i̇😀山'


ast            : lower(NULL)
raw expr       : lower(NULL)
checked expr   : lower<String NULL>(CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : lcase(a)
raw expr       : lcase(a::String)
checked expr   : lower<String>(a)
evaluation:
+--------+-------------------+-------------+
|        | a                 | Output      |
+--------+-------------------+-------------+
| Type   | String            | String      |
| Domain | {"Abc"..="İ😀山"} | {""..}      |
| Row 0  | 'Abc'             | 'abc'       |
| Row 1  | 'DOBRÝ DEN'       | 'dobrý den' |
| Row 2  | 'İ😀山'           | 'i̇😀山'     |
+--------+-------------------+-------------+
evaluation (internal):
+--------+-------------------------------------+
| Column | Data                                |
+--------+-------------------------------------+
| a      | StringColumn[Abc, DOBRÝ DEN, İ😀山] |
| Output | StringColumn[abc, dobrý den, i̇😀山] |
+--------+-------------------------------------+


ast            : bit_length('latin')
raw expr       : bit_length('latin')
checked expr   : bit_length<String>("latin")
optimized expr : 40_u64
output type    : UInt64
output domain  : {40..=40}
output         : 40


ast            : bit_length('CAFÉ')
raw expr       : bit_length('CAFÉ')
checked expr   : bit_length<String>("CAFÉ")
optimized expr : 40_u64
output type    : UInt64
output domain  : {40..=40}
output         : 40


ast            : bit_length('数据库')
raw expr       : bit_length('数据库')
checked expr   : bit_length<String>("数据库")
optimized expr : 72_u64
output type    : UInt64
output domain  : {72..=72}
output         : 72


ast            : bit_length('НОЧЬ НА ОКРАИНЕ МОСКВЫ')
raw expr       : bit_length('НОЧЬ НА ОКРАИНЕ МОСКВЫ')
checked expr   : bit_length<String>("НОЧЬ НА ОКРАИНЕ МОСКВЫ")
optimized expr : 328_u64
output type    : UInt64
output domain  : {328..=328}
output         : 328


ast            : bit_length('قاعدة البيانات')
raw expr       : bit_length('قاعدة البيانات')
checked expr   : bit_length<String>("قاعدة البيانات")
optimized expr : 216_u64
output type    : UInt64
output domain  : {216..=216}
output         : 216


ast            : bit_length(NULL)
raw expr       : bit_length(NULL)
checked expr   : bit_length<String NULL>(CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : UInt64 NULL
output domain  : {NULL}
output         : NULL


ast            : bit_length(a)
raw expr       : bit_length(a::String)
checked expr   : bit_length<String>(a)
evaluation:
+--------+-----------------------------------+----------------------------+
|        | a                                 | Output                     |
+--------+-----------------------------------+----------------------------+
| Type   | String                            | UInt64                     |
| Domain | {"latin"..="кириллица and latin"} | {0..=18446744073709551615} |
| Row 0  | 'latin'                           | 40                         |
| Row 1  | 'кириллица'                       | 144                        |
| Row 2  | 'кириллица and latin'             | 224                        |
+--------+-----------------------------------+----------------------------+
evaluation (internal):
+--------+-----------------------------------------------------+
| Column | Data                                                |
+--------+-----------------------------------------------------+
| a      | StringColumn[latin, кириллица, кириллица and latin] |
| Output | UInt64([40, 144, 224])                              |
+--------+-----------------------------------------------------+


ast            : octet_length('latin')
raw expr       : octet_length('latin')
checked expr   : octet_length<String>("latin")
optimized expr : 5_u64
output type    : UInt64
output domain  : {5..=5}
output         : 5


ast            : octet_length('CAFÉ')
raw expr       : octet_length('CAFÉ')
checked expr   : octet_length<String>("CAFÉ")
optimized expr : 5_u64
output type    : UInt64
output domain  : {5..=5}
output         : 5


ast            : octet_length('数据库')
raw expr       : octet_length('数据库')
checked expr   : octet_length<String>("数据库")
optimized expr : 9_u64
output type    : UInt64
output domain  : {9..=9}
output         : 9


ast            : octet_length('НОЧЬ НА ОКРАИНЕ МОСКВЫ')
raw expr       : octet_length('НОЧЬ НА ОКРАИНЕ МОСКВЫ')
checked expr   : octet_length<String>("НОЧЬ НА ОКРАИНЕ МОСКВЫ")
optimized expr : 41_u64
output type    : UInt64
output domain  : {41..=41}
output         : 41


ast            : octet_length('قاعدة البيانات')
raw expr       : octet_length('قاعدة البيانات')
checked expr   : octet_length<String>("قاعدة البيانات")
optimized expr : 27_u64
output type    : UInt64
output domain  : {27..=27}
output         : 27


ast            : octet_length(NULL)
raw expr       : octet_length(NULL)
checked expr   : octet_length<String NULL>(CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : UInt64 NULL
output domain  : {NULL}
output         : NULL


ast            : octet_length(a)
raw expr       : octet_length(a::String)
checked expr   : octet_length<String>(a)
evaluation:
+--------+-----------------------------------+----------------------------+
|        | a                                 | Output                     |
+--------+-----------------------------------+----------------------------+
| Type   | String                            | UInt64                     |
| Domain | {"latin"..="кириллица and latin"} | {0..=18446744073709551615} |
| Row 0  | 'latin'                           | 5                          |
| Row 1  | 'кириллица'                       | 18                         |
| Row 2  | 'кириллица and latin'             | 28                         |
+--------+-----------------------------------+----------------------------+
evaluation (internal):
+--------+-----------------------------------------------------+
| Column | Data                                                |
+--------+-----------------------------------------------------+
| a      | StringColumn[latin, кириллица, кириллица and latin] |
| Output | UInt64([5, 18, 28])                                 |
+--------+-----------------------------------------------------+


ast            : char_length('latin')
raw expr       : char_length('latin')
checked expr   : length<String>("latin")
optimized expr : 5_u64
output type    : UInt64
output domain  : {5..=5}
output         : 5


ast            : char_length('CAFÉ')
raw expr       : char_length('CAFÉ')
checked expr   : length<String>("CAFÉ")
optimized expr : 4_u64
output type    : UInt64
output domain  : {4..=4}
output         : 4


ast            : char_length('数据库')
raw expr       : char_length('数据库')
checked expr   : length<String>("数据库")
optimized expr : 3_u64
output type    : UInt64
output domain  : {3..=3}
output         : 3


ast            : char_length('НОЧЬ НА ОКРАИНЕ МОСКВЫ')
raw expr       : char_length('НОЧЬ НА ОКРАИНЕ МОСКВЫ')
checked expr   : length<String>("НОЧЬ НА ОКРАИНЕ МОСКВЫ")
optimized expr : 22_u64
output type    : UInt64
output domain  : {22..=22}
output         : 22


ast            : char_length('قاعدة البيانات')
raw expr       : char_length('قاعدة البيانات')
checked expr   : length<String>("قاعدة البيانات")
optimized expr : 14_u64
output type    : UInt64
output domain  : {14..=14}
output         : 14


ast            : char_length(NULL)
raw expr       : char_length(NULL)
checked expr   : length<Variant NULL>(CAST<NULL>(NULL AS Variant NULL))
optimized expr : NULL
output type    : UInt32 NULL
output domain  : {NULL}
output         : NULL


ast            : character_length(a)
raw expr       : character_length(a::String)
checked expr   : length<String>(a)
evaluation:
+--------+-----------------------------------+----------------------------+
|        | a                                 | Output                     |
+--------+-----------------------------------+----------------------------+
| Type   | String                            | UInt64                     |
| Domain | {"latin"..="кириллица and latin"} | {0..=18446744073709551615} |
| Row 0  | 'latin'                           | 5                          |
| Row 1  | 'кириллица'                       | 9                          |
| Row 2  | 'кириллица and latin'             | 19                         |
+--------+-----------------------------------+----------------------------+
evaluation (internal):
+--------+-----------------------------------------------------+
| Column | Data                                                |
+--------+-----------------------------------------------------+
| a      | StringColumn[latin, кириллица, кириллица and latin] |
| Output | UInt64([5, 9, 19])                                  |
+--------+-----------------------------------------------------+


ast            : quote('a\0b')
raw expr       : quote('a\x00b')
checked expr   : quote<String>("a\0b")
optimized expr : "a\\0b"
output type    : String
output domain  : {"a\\0b"..="a\\0b"}
output         : 'a\\0b'


ast            : quote('a\'b')
raw expr       : quote('a\'b')
checked expr   : quote<String>("a'b")
optimized expr : "a\\'b"
output type    : String
output domain  : {"a\\'b"..="a\\'b"}
output         : 'a\\\'b'


ast            : quote('a\"b')
raw expr       : quote('a"b')
checked expr   : quote<String>("a\"b")
optimized expr : "a\\\"b"
output type    : String
output domain  : {"a\\\"b"..="a\\\"b"}
output         : 'a\\"b'


ast            : quote('a\bb')
raw expr       : quote('a\x08b')
checked expr   : quote<String>("a\u{8}b")
optimized expr : "a\\bb"
output type    : String
output domain  : {"a\\bb"..="a\\bb"}
output         : 'a\\bb'


ast            : quote('a\nb')
raw expr       : quote('a\nb')
checked expr   : quote<String>("a\nb")
optimized expr : "a\\nb"
output type    : String
output domain  : {"a\\nb"..="a\\nb"}
output         : 'a\\nb'


ast            : quote('a\rb')
raw expr       : quote('a\rb')
checked expr   : quote<String>("a\rb")
optimized expr : "a\\rb"
output type    : String
output domain  : {"a\\rb"..="a\\rb"}
output         : 'a\\rb'


ast            : quote('a\tb')
raw expr       : quote('a\tb')
checked expr   : quote<String>("a\tb")
optimized expr : "a\\tb"
output type    : String
output domain  : {"a\\tb"..="a\\tb"}
output         : 'a\\tb'


ast            : quote('a\\b')
raw expr       : quote('a\\b')
checked expr   : quote<String>("a\\b")
optimized expr : "a\\\\b"
output type    : String
output domain  : {"a\\\\b"..="a\\\\b"}
output         : 'a\\\\b'


ast            : quote('你好')
raw expr       : quote('你好')
checked expr   : quote<String>("你好")
optimized expr : "你好"
output type    : String
output domain  : {"你好"..="你好"}
output         : '你好'


ast            : quote('ß😀山')
raw expr       : quote('ß😀山')
checked expr   : quote<String>("ß😀山")
optimized expr : "ß😀山"
output type    : String
output domain  : {"ß😀山"..="ß😀山"}
output         : 'ß😀山'


ast            : quote('Dobrý den')
raw expr       : quote('Dobrý den')
checked expr   : quote<String>("Dobrý den")
optimized expr : "Dobrý den"
output type    : String
output domain  : {"Dobrý den"..="Dobrý den"}
output         : 'Dobrý den'


ast            : quote(Null)
raw expr       : quote(NULL)
checked expr   : quote<String NULL>(CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : quote(a)
raw expr       : quote(a::String)
checked expr   : quote<String>(a)
evaluation:
+--------+---------------------+--------------+
|        | a                   | Output       |
+--------+---------------------+--------------+
| Type   | String              | String       |
| Domain | {"a\\'b"..="a\\nb"} | {""..}       |
| Row 0  | 'a\\0b'             | 'a\\\\0b'    |
| Row 1  | 'a\\\'b'            | 'a\\\\\\\'b' |
| Row 2  | 'a\\nb'             | 'a\\\\nb'    |
+--------+---------------------+--------------+
evaluation (internal):
+--------+------------------------------------+
| Column | Data                               |
+--------+------------------------------------+
| a      | StringColumn[a\0b, a\'b, a\nb]     |
| Output | StringColumn[a\\0b, a\\\'b, a\\nb] |
+--------+------------------------------------+


ast            : reverse('abc')
raw expr       : reverse('abc')
checked expr   : reverse<String>("abc")
optimized expr : "cba"
output type    : String
output domain  : {"cba"..="cba"}
output         : 'cba'


ast            : reverse('a')
raw expr       : reverse('a')
checked expr   : reverse<String>("a")
optimized expr : "a"
output type    : String
output domain  : {"a"..="a"}
output         : 'a'


ast            : reverse('')
raw expr       : reverse('')
checked expr   : reverse<String>("")
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : reverse('你好')
raw expr       : reverse('你好')
checked expr   : reverse<String>("你好")
optimized expr : "好你"
output type    : String
output domain  : {"好你"..="好你"}
output         : '好你'


ast            : reverse('ß😀山')
raw expr       : reverse('ß😀山')
checked expr   : reverse<String>("ß😀山")
optimized expr : "山😀ß"
output type    : String
output domain  : {"山😀ß"..="山😀ß"}
output         : '山😀ß'


ast            : reverse('Dobrý den')
raw expr       : reverse('Dobrý den')
checked expr   : reverse<String>("Dobrý den")
optimized expr : "ned ýrboD"
output type    : String
output domain  : {"ned ýrboD"..="ned ýrboD"}
output         : 'ned ýrboD'


ast            : reverse(Null)
raw expr       : reverse(NULL)
checked expr   : reverse<String NULL>(CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : reverse(a)
raw expr       : reverse(a::String)
checked expr   : reverse<String>(a)
evaluation:
+--------+--------------+--------+
|        | a            | Output |
+--------+--------------+--------+
| Type   | String       | String |
| Domain | {""..="abc"} | {""..} |
| Row 0  | 'abc'        | 'cba'  |
| Row 1  | 'a'          | 'a'    |
| Row 2  | ''           | ''     |
+--------+--------------+--------+
evaluation (internal):
+--------+------------------------+
| Column | Data                   |
+--------+------------------------+
| a      | StringColumn[abc, a, ] |
| Output | StringColumn[cba, a, ] |
+--------+------------------------+


ast            : ascii('1')
raw expr       : ascii('1')
checked expr   : ascii<String>("1")
optimized expr : 49_u8
output type    : UInt8
output domain  : {49..=49}
output         : 49


ast            : ascii('123')
raw expr       : ascii('123')
checked expr   : ascii<String>("123")
optimized expr : 49_u8
output type    : UInt8
output domain  : {49..=49}
output         : 49


ast            : ascii('-1')
raw expr       : ascii('-1')
checked expr   : ascii<String>("-1")
optimized expr : 45_u8
output type    : UInt8
output domain  : {45..=45}
output         : 45


ast            : ascii('')
raw expr       : ascii('')
checked expr   : ascii<String>("")
optimized expr : 0_u8
output type    : UInt8
output domain  : {0..=0}
output         : 0


ast            : ascii('你好')
raw expr       : ascii('你好')
checked expr   : ascii<String>("你好")
optimized expr : 228_u8
output type    : UInt8
output domain  : {228..=228}
output         : 228


ast            : ascii('😀123')
raw expr       : ascii('😀123')
checked expr   : ascii<String>("😀123")
optimized expr : 240_u8
output type    : UInt8
output domain  : {240..=240}
output         : 240


ast            : ascii(Null)
raw expr       : ascii(NULL)
checked expr   : ascii<String NULL>(CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : UInt8 NULL
output domain  : {NULL}
output         : NULL


ast            : ascii(a)
raw expr       : ascii(a::String)
checked expr   : ascii<String>(a)
evaluation:
+--------+-----------------+------------+
|        | a               | Output     |
+--------+-----------------+------------+
| Type   | String          | UInt8      |
| Domain | {"-1"..="你好"} | {45..=228} |
| Row 0  | '1'             | 49         |
| Row 1  | '123'           | 49         |
| Row 2  | '-1'            | 45         |
| Row 3  | '你好'          | 228        |
+--------+-----------------+------------+
evaluation (internal):
+--------+--------------------------------+
| Column | Data                           |
+--------+--------------------------------+
| a      | StringColumn[1, 123, -1, 你好] |
| Output | UInt8([49, 49, 45, 228])       |
+--------+--------------------------------+


ast            : ascii(b)
raw expr       : ascii(b::String)
checked expr   : ascii<String>(b)
optimized expr : 0_u8
evaluation:
+--------+-----------+---------+
|        | b         | Output  |
+--------+-----------+---------+
| Type   | String    | UInt8   |
| Domain | {""..=""} | {0..=0} |
| Row 0  | ''        | 0       |
+--------+-----------+---------+
evaluation (internal):
+--------+----------------+
| Column | Data           |
+--------+----------------+
| b      | StringColumn[] |
| Output | UInt8([0])     |
+--------+----------------+


ast            : ltrim('   abc   ')
raw expr       : ltrim('   abc   ')
checked expr   : ltrim<String>("   abc   ")
optimized expr : "abc   "
output type    : String
output domain  : {"abc   "..="abc   "}
output         : 'abc   '


ast            : ltrim('  ')
raw expr       : ltrim('  ')
checked expr   : ltrim<String>("  ")
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : ltrim('  你  好  ')
raw expr       : ltrim('  你  好  ')
checked expr   : ltrim<String>("  你  好  ")
optimized expr : "你  好  "
output type    : String
output domain  : {"你  好  "..="你  好  "}
output         : '你  好  '


ast            : ltrim('  분산 데이터베이스    ')
raw expr       : ltrim('  분산 데이터베이스    ')
checked expr   : ltrim<String>("  분산 데이터베이스    ")
optimized expr : "분산 데이터베이스    "
output type    : String
output domain  : {"분산 데이터베이스    "..="분산 데이터베이스    "}
output         : '분산 데이터베이스    '


ast            : ltrim('   あなたのことが好きです   ')
raw expr       : ltrim('   あなたのことが好きです   ')
checked expr   : ltrim<String>("   あなたのことが好きです   ")
optimized expr : "あなたのことが好きです   "
output type    : String
output domain  : {"あなたのことが好きです   "..="あなたのことが好きです   "}
output         : 'あなたのことが好きです   '


ast            : ltrim(NULL)
raw expr       : ltrim(NULL)
checked expr   : ltrim<String NULL>(CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : ltrim(a)
raw expr       : ltrim(a::String)
checked expr   : ltrim<String>(a)
evaluation:
+--------+-----------------------+----------+
|        | a                     | Output   |
+--------+-----------------------+----------+
| Type   | String                | String   |
| Domain | {"   abc"..="abc   "} | {""..}   |
| Row 0  | 'abc'                 | 'abc'    |
| Row 1  | '   abc'              | 'abc'    |
| Row 2  | '   abc   '           | 'abc   ' |
| Row 3  | 'abc   '              | 'abc   ' |
+--------+-----------------------+----------+
evaluation (internal):
+--------+----------------------------------------------+
| Column | Data                                         |
+--------+----------------------------------------------+
| a      | StringColumn[abc,    abc,    abc   , abc   ] |
| Output | StringColumn[abc, abc, abc   , abc   ]       |
+--------+----------------------------------------------+


ast            : ltrim(' aa','')
raw expr       : ltrim(' aa', '')
checked expr   : ltrim<String, String>(" aa", "")
optimized expr : " aa"
output type    : String
output domain  : {" aa"..=" aa"}
output         : ' aa'


ast            : ltrim('\taa')
raw expr       : ltrim('\taa')
checked expr   : ltrim<String>("\taa")
optimized expr : "\taa"
output type    : String
output domain  : {"\taa"..="\taa"}
output         : '\taa'


ast            : ltrim('#000000123','0#')
raw expr       : ltrim('#000000123', '0#')
checked expr   : ltrim<String, String>("#000000123", "0#")
optimized expr : "123"
output type    : String
output domain  : {"123"..="123"}
output         : '123'


ast            : rtrim('   abc   ')
raw expr       : rtrim('   abc   ')
checked expr   : rtrim<String>("   abc   ")
optimized expr : "   abc"
output type    : String
output domain  : {"   abc"..="   abc"}
output         : '   abc'


ast            : rtrim('  ')
raw expr       : rtrim('  ')
checked expr   : rtrim<String>("  ")
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : rtrim('  你  好  ')
raw expr       : rtrim('  你  好  ')
checked expr   : rtrim<String>("  你  好  ")
optimized expr : "  你  好"
output type    : String
output domain  : {"  你  好"..="  你  好"}
output         : '  你  好'


ast            : rtrim('  분산 데이터베이스    ')
raw expr       : rtrim('  분산 데이터베이스    ')
checked expr   : rtrim<String>("  분산 데이터베이스    ")
optimized expr : "  분산 데이터베이스"
output type    : String
output domain  : {"  분산 데이터베이스"..="  분산 데이터베이스"}
output         : '  분산 데이터베이스'


ast            : rtrim('   あなたのことが好きです   ')
raw expr       : rtrim('   あなたのことが好きです   ')
checked expr   : rtrim<String>("   あなたのことが好きです   ")
optimized expr : "   あなたのことが好きです"
output type    : String
output domain  : {"   あなたのことが好きです"..="   あなたのことが好きです"}
output         : '   あなたのことが好きです'


ast            : rtrim(NULL)
raw expr       : rtrim(NULL)
checked expr   : rtrim<String NULL>(CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : rtrim(a)
raw expr       : rtrim(a::String)
checked expr   : rtrim<String>(a)
evaluation:
+--------+-----------------------+----------+
|        | a                     | Output   |
+--------+-----------------------+----------+
| Type   | String                | String   |
| Domain | {"   abc"..="abc   "} | {""..}   |
| Row 0  | 'abc'                 | 'abc'    |
| Row 1  | '   abc'              | '   abc' |
| Row 2  | '   abc   '           | '   abc' |
| Row 3  | 'abc   '              | 'abc'    |
+--------+-----------------------+----------+
evaluation (internal):
+--------+----------------------------------------------+
| Column | Data                                         |
+--------+----------------------------------------------+
| a      | StringColumn[abc,    abc,    abc   , abc   ] |
| Output | StringColumn[abc,    abc,    abc, abc]       |
+--------+----------------------------------------------+


ast            : rtrim('aa ','')
raw expr       : rtrim('aa ', '')
checked expr   : rtrim<String, String>("aa ", "")
optimized expr : "aa "
output type    : String
output domain  : {"aa "..="aa "}
output         : 'aa '


ast            : rtrim('aa\t')
raw expr       : rtrim('aa\t')
checked expr   : rtrim<String>("aa\t")
optimized expr : "aa\t"
output type    : String
output domain  : {"aa\t"..="aa\t"}
output         : 'aa\t'


ast            : rtrim('$125.00','0.')
raw expr       : rtrim('$125.00', '0.')
checked expr   : rtrim<String, String>("$125.00", "0.")
optimized expr : "$125"
output type    : String
output domain  : {"$125"..="$125"}
output         : '$125'


ast            : trim_leading('aaabbaaa', 'a')
raw expr       : trim_leading('aaabbaaa', 'a')
checked expr   : trim_leading<String, String>("aaabbaaa", "a")
optimized expr : "bbaaa"
output type    : String
output domain  : {"bbaaa"..="bbaaa"}
output         : 'bbaaa'


ast            : trim_leading('aaabbaaa', 'aa')
raw expr       : trim_leading('aaabbaaa', 'aa')
checked expr   : trim_leading<String, String>("aaabbaaa", "aa")
optimized expr : "abbaaa"
output type    : String
output domain  : {"abbaaa"..="abbaaa"}
output         : 'abbaaa'


ast            : trim_leading('aaaaaaaa', 'a')
raw expr       : trim_leading('aaaaaaaa', 'a')
checked expr   : trim_leading<String, String>("aaaaaaaa", "a")
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : trim_leading('aaabbaaa', 'b')
raw expr       : trim_leading('aaabbaaa', 'b')
checked expr   : trim_leading<String, String>("aaabbaaa", "b")
optimized expr : "aaabbaaa"
output type    : String
output domain  : {"aaabbaaa"..="aaabbaaa"}
output         : 'aaabbaaa'


ast            : trim_leading(NULL, 'a')
raw expr       : trim_leading(NULL, 'a')
checked expr   : trim_leading<String NULL, String NULL>(CAST<NULL>(NULL AS String NULL), CAST<String>("a" AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : trim_leading('aaaaaaaa', NULL)
raw expr       : trim_leading('aaaaaaaa', NULL)
checked expr   : trim_leading<String NULL, String NULL>(CAST<String>("aaaaaaaa" AS String NULL), CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : trim_leading('aaaaaaaa', '')
raw expr       : trim_leading('aaaaaaaa', '')
checked expr   : trim_leading<String, String>("aaaaaaaa", "")
optimized expr : "aaaaaaaa"
output type    : String
output domain  : {"aaaaaaaa"..="aaaaaaaa"}
output         : 'aaaaaaaa'


ast            : trim_leading('분산 데이터베이스', '분산 ')
raw expr       : trim_leading('분산 데이터베이스', '분산 ')
checked expr   : trim_leading<String, String>("분산 데이터베이스", "분산 ")
optimized expr : "데이터베이스"
output type    : String
output domain  : {"데이터베이스"..="데이터베이스"}
output         : '데이터베이스'


ast            : trim_leading('あなたのことが好きです', 'あなたの')
raw expr       : trim_leading('あなたのことが好きです', 'あなたの')
checked expr   : trim_leading<String, String>("あなたのことが好きです", "あなたの")
optimized expr : "ことが好きです"
output type    : String
output domain  : {"ことが好きです"..="ことが好きです"}
output         : 'ことが好きです'


ast            : trim_leading(a, 'a')
raw expr       : trim_leading(a::String, 'a')
checked expr   : trim_leading<String, String>(a, "a")
evaluation:
+--------+-----------------------+----------+
|        | a                     | Output   |
+--------+-----------------------+----------+
| Type   | String                | String   |
| Domain | {"aabbaa"..="ccddcc"} | {""..}   |
| Row 0  | 'aabbaa'              | 'bbaa'   |
| Row 1  | 'bbccbb'              | 'bbccbb' |
| Row 2  | 'ccddcc'              | 'ccddcc' |
| Row 3  | 'aabbaa'              | 'bbaa'   |
+--------+-----------------------+----------+
evaluation (internal):
+--------+----------------------------------------------+
| Column | Data                                         |
+--------+----------------------------------------------+
| a      | StringColumn[aabbaa, bbccbb, ccddcc, aabbaa] |
| Output | StringColumn[bbaa, bbccbb, ccddcc, bbaa]     |
+--------+----------------------------------------------+


ast            : trim_leading(a, b)
raw expr       : trim_leading(a::String, b::String)
checked expr   : trim_leading<String, String>(a, b)
evaluation:
+--------+-----------------------+------------+----------+
|        | a                     | b          | Output   |
+--------+-----------------------+------------+----------+
| Type   | String                | String     | String   |
| Domain | {"aabbaa"..="ccddcc"} | {""..="c"} | {""..}   |
| Row 0  | 'aabbaa'              | 'a'        | 'bbaa'   |
| Row 1  | 'bbccbb'              | 'b'        | 'ccbb'   |
| Row 2  | 'ccddcc'              | 'c'        | 'ddcc'   |
| Row 3  | 'aabbaa'              | ''         | 'aabbaa' |
+--------+-----------------------+------------+----------+
evaluation (internal):
+--------+----------------------------------------------+
| Column | Data                                         |
+--------+----------------------------------------------+
| a      | StringColumn[aabbaa, bbccbb, ccddcc, aabbaa] |
| b      | StringColumn[a, b, c, ]                      |
| Output | StringColumn[bbaa, ccbb, ddcc, aabbaa]       |
+--------+----------------------------------------------+


ast            : trim_leading('aba', b)
raw expr       : trim_leading('aba', b::String)
checked expr   : trim_leading<String, String>("aba", b)
evaluation:
+--------+------------+--------+
|        | b          | Output |
+--------+------------+--------+
| Type   | String     | String |
| Domain | {""..="c"} | {""..} |
| Row 0  | 'a'        | 'ba'   |
| Row 1  | 'b'        | 'aba'  |
| Row 2  | 'c'        | 'aba'  |
| Row 3  | ''         | 'aba'  |
+--------+------------+--------+
evaluation (internal):
+--------+---------------------------------+
| Column | Data                            |
+--------+---------------------------------+
| b      | StringColumn[a, b, c, ]         |
| Output | StringColumn[ba, aba, aba, aba] |
+--------+---------------------------------+


ast            : trim_trailing('aaabbaaa', 'a')
raw expr       : trim_trailing('aaabbaaa', 'a')
checked expr   : trim_trailing<String, String>("aaabbaaa", "a")
optimized expr : "aaabb"
output type    : String
output domain  : {"aaabb"..="aaabb"}
output         : 'aaabb'


ast            : trim_trailing('aaabbaaa', 'aa')
raw expr       : trim_trailing('aaabbaaa', 'aa')
checked expr   : trim_trailing<String, String>("aaabbaaa", "aa")
optimized expr : "aaabba"
output type    : String
output domain  : {"aaabba"..="aaabba"}
output         : 'aaabba'


ast            : trim_trailing('aaaaaaaa', 'a')
raw expr       : trim_trailing('aaaaaaaa', 'a')
checked expr   : trim_trailing<String, String>("aaaaaaaa", "a")
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : trim_trailing('aaabbaaa', 'b')
raw expr       : trim_trailing('aaabbaaa', 'b')
checked expr   : trim_trailing<String, String>("aaabbaaa", "b")
optimized expr : "aaabbaaa"
output type    : String
output domain  : {"aaabbaaa"..="aaabbaaa"}
output         : 'aaabbaaa'


ast            : trim_trailing(NULL, 'a')
raw expr       : trim_trailing(NULL, 'a')
checked expr   : trim_trailing<String NULL, String NULL>(CAST<NULL>(NULL AS String NULL), CAST<String>("a" AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : trim_trailing('aaaaaaaa', NULL)
raw expr       : trim_trailing('aaaaaaaa', NULL)
checked expr   : trim_trailing<String NULL, String NULL>(CAST<String>("aaaaaaaa" AS String NULL), CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : trim_trailing('aaaaaaaa', '')
raw expr       : trim_trailing('aaaaaaaa', '')
checked expr   : trim_trailing<String, String>("aaaaaaaa", "")
optimized expr : "aaaaaaaa"
output type    : String
output domain  : {"aaaaaaaa"..="aaaaaaaa"}
output         : 'aaaaaaaa'


ast            : trim_trailing('분산 데이터베이스', '베이스')
raw expr       : trim_trailing('분산 데이터베이스', '베이스')
checked expr   : trim_trailing<String, String>("분산 데이터베이스", "베이스")
optimized expr : "분산 데이터"
output type    : String
output domain  : {"분산 데이터"..="분산 데이터"}
output         : '분산 데이터'


ast            : trim_trailing('あなたのことが好きです', '好きです')
raw expr       : trim_trailing('あなたのことが好きです', '好きです')
checked expr   : trim_trailing<String, String>("あなたのことが好きです", "好きです")
optimized expr : "あなたのことが"
output type    : String
output domain  : {"あなたのことが"..="あなたのことが"}
output         : 'あなたのことが'


ast            : trim_trailing(a, 'b')
raw expr       : trim_trailing(a::String, 'b')
checked expr   : trim_trailing<String, String>(a, "b")
evaluation:
+--------+-----------------------+----------+
|        | a                     | Output   |
+--------+-----------------------+----------+
| Type   | String                | String   |
| Domain | {"aabbaa"..="ccddcc"} | {""..}   |
| Row 0  | 'aabbaa'              | 'aabbaa' |
| Row 1  | 'bbccbb'              | 'bbcc'   |
| Row 2  | 'ccddcc'              | 'ccddcc' |
| Row 3  | 'aabbaa'              | 'aabbaa' |
+--------+-----------------------+----------+
evaluation (internal):
+--------+----------------------------------------------+
| Column | Data                                         |
+--------+----------------------------------------------+
| a      | StringColumn[aabbaa, bbccbb, ccddcc, aabbaa] |
| Output | StringColumn[aabbaa, bbcc, ccddcc, aabbaa]   |
+--------+----------------------------------------------+


ast            : trim_trailing(a, b)
raw expr       : trim_trailing(a::String, b::String)
checked expr   : trim_trailing<String, String>(a, b)
evaluation:
+--------+-----------------------+------------+----------+
|        | a                     | b          | Output   |
+--------+-----------------------+------------+----------+
| Type   | String                | String     | String   |
| Domain | {"aabbaa"..="ccddcc"} | {""..="c"} | {""..}   |
| Row 0  | 'aabbaa'              | 'a'        | 'aabb'   |
| Row 1  | 'bbccbb'              | 'b'        | 'bbcc'   |
| Row 2  | 'ccddcc'              | 'c'        | 'ccdd'   |
| Row 3  | 'aabbaa'              | ''         | 'aabbaa' |
+--------+-----------------------+------------+----------+
evaluation (internal):
+--------+----------------------------------------------+
| Column | Data                                         |
+--------+----------------------------------------------+
| a      | StringColumn[aabbaa, bbccbb, ccddcc, aabbaa] |
| b      | StringColumn[a, b, c, ]                      |
| Output | StringColumn[aabb, bbcc, ccdd, aabbaa]       |
+--------+----------------------------------------------+


ast            : trim_trailing('aba', b)
raw expr       : trim_trailing('aba', b::String)
checked expr   : trim_trailing<String, String>("aba", b)
evaluation:
+--------+------------+--------+
|        | b          | Output |
+--------+------------+--------+
| Type   | String     | String |
| Domain | {""..="c"} | {""..} |
| Row 0  | 'a'        | 'ab'   |
| Row 1  | 'b'        | 'aba'  |
| Row 2  | 'c'        | 'aba'  |
| Row 3  | ''         | 'aba'  |
+--------+------------+--------+
evaluation (internal):
+--------+---------------------------------+
| Column | Data                            |
+--------+---------------------------------+
| b      | StringColumn[a, b, c, ]         |
| Output | StringColumn[ab, aba, aba, aba] |
+--------+---------------------------------+


ast            : trim_both('aaabbaaa', 'a')
raw expr       : trim_both('aaabbaaa', 'a')
checked expr   : trim_both<String, String>("aaabbaaa", "a")
optimized expr : "bb"
output type    : String
output domain  : {"bb"..="bb"}
output         : 'bb'


ast            : trim_both('aaabbaaa', 'aa')
raw expr       : trim_both('aaabbaaa', 'aa')
checked expr   : trim_both<String, String>("aaabbaaa", "aa")
optimized expr : "abba"
output type    : String
output domain  : {"abba"..="abba"}
output         : 'abba'


ast            : trim_both('aaaaaaaa', 'a')
raw expr       : trim_both('aaaaaaaa', 'a')
checked expr   : trim_both<String, String>("aaaaaaaa", "a")
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : trim_both('aaabbaaa', 'b')
raw expr       : trim_both('aaabbaaa', 'b')
checked expr   : trim_both<String, String>("aaabbaaa", "b")
optimized expr : "aaabbaaa"
output type    : String
output domain  : {"aaabbaaa"..="aaabbaaa"}
output         : 'aaabbaaa'


ast            : trim_both(NULL, 'a')
raw expr       : trim_both(NULL, 'a')
checked expr   : trim_both<String NULL, String NULL>(CAST<NULL>(NULL AS String NULL), CAST<String>("a" AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : trim_both('aaaaaaaa', NULL)
raw expr       : trim_both('aaaaaaaa', NULL)
checked expr   : trim_both<String NULL, String NULL>(CAST<String>("aaaaaaaa" AS String NULL), CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : trim_both('aaaaaaaa', '')
raw expr       : trim_both('aaaaaaaa', '')
checked expr   : trim_both<String, String>("aaaaaaaa", "")
optimized expr : "aaaaaaaa"
output type    : String
output domain  : {"aaaaaaaa"..="aaaaaaaa"}
output         : 'aaaaaaaa'


ast            : trim_both('  你  好  ', ' ')
raw expr       : trim_both('  你  好  ', ' ')
checked expr   : trim_both<String, String>("  你  好  ", " ")
optimized expr : "你  好"
output type    : String
output domain  : {"你  好"..="你  好"}
output         : '你  好'


ast            : trim_both('  분산 데이터베이스    ', ' ')
raw expr       : trim_both('  분산 데이터베이스    ', ' ')
checked expr   : trim_both<String, String>("  분산 데이터베이스    ", " ")
optimized expr : "분산 데이터베이스"
output type    : String
output domain  : {"분산 데이터베이스"..="분산 데이터베이스"}
output         : '분산 데이터베이스'


ast            : trim_both('   あなたのことが好きです   ', ' ')
raw expr       : trim_both('   あなたのことが好きです   ', ' ')
checked expr   : trim_both<String, String>("   あなたのことが好きです   ", " ")
optimized expr : "あなたのことが好きです"
output type    : String
output domain  : {"あなたのことが好きです"..="あなたのことが好きです"}
output         : 'あなたのことが好きです'


ast            : trim_both(a, 'a')
raw expr       : trim_both(a::String, 'a')
checked expr   : trim_both<String, String>(a, "a")
evaluation:
+--------+-----------------------+----------+
|        | a                     | Output   |
+--------+-----------------------+----------+
| Type   | String                | String   |
| Domain | {"aabbaa"..="ccddcc"} | {""..}   |
| Row 0  | 'aabbaa'              | 'bb'     |
| Row 1  | 'bbccbb'              | 'bbccbb' |
| Row 2  | 'ccddcc'              | 'ccddcc' |
| Row 3  | 'aabbaa'              | 'bb'     |
+--------+-----------------------+----------+
evaluation (internal):
+--------+----------------------------------------------+
| Column | Data                                         |
+--------+----------------------------------------------+
| a      | StringColumn[aabbaa, bbccbb, ccddcc, aabbaa] |
| Output | StringColumn[bb, bbccbb, ccddcc, bb]         |
+--------+----------------------------------------------+


ast            : trim_both(a, b)
raw expr       : trim_both(a::String, b::String)
checked expr   : trim_both<String, String>(a, b)
evaluation:
+--------+-----------------------+------------+----------+
|        | a                     | b          | Output   |
+--------+-----------------------+------------+----------+
| Type   | String                | String     | String   |
| Domain | {"aabbaa"..="ccddcc"} | {""..="c"} | {""..}   |
| Row 0  | 'aabbaa'              | 'a'        | 'bb'     |
| Row 1  | 'bbccbb'              | 'b'        | 'cc'     |
| Row 2  | 'ccddcc'              | 'c'        | 'dd'     |
| Row 3  | 'aabbaa'              | ''         | 'aabbaa' |
+--------+-----------------------+------------+----------+
evaluation (internal):
+--------+----------------------------------------------+
| Column | Data                                         |
+--------+----------------------------------------------+
| a      | StringColumn[aabbaa, bbccbb, ccddcc, aabbaa] |
| b      | StringColumn[a, b, c, ]                      |
| Output | StringColumn[bb, cc, dd, aabbaa]             |
+--------+----------------------------------------------+


ast            : trim_both('aba', b)
raw expr       : trim_both('aba', b::String)
checked expr   : trim_both<String, String>("aba", b)
evaluation:
+--------+------------+--------+
|        | b          | Output |
+--------+------------+--------+
| Type   | String     | String |
| Domain | {""..="c"} | {""..} |
| Row 0  | 'a'        | 'b'    |
| Row 1  | 'b'        | 'aba'  |
| Row 2  | 'c'        | 'aba'  |
| Row 3  | ''         | 'aba'  |
+--------+------------+--------+
evaluation (internal):
+--------+--------------------------------+
| Column | Data                           |
+--------+--------------------------------+
| b      | StringColumn[a, b, c, ]        |
| Output | StringColumn[b, aba, aba, aba] |
+--------+--------------------------------+


ast            : trim('   abc   ')
raw expr       : trim('   abc   ')
checked expr   : trim<String>("   abc   ")
optimized expr : "abc"
output type    : String
output domain  : {"abc"..="abc"}
output         : 'abc'


ast            : trim('  ')
raw expr       : trim('  ')
checked expr   : trim<String>("  ")
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : trim('  你  好  ')
raw expr       : trim('  你  好  ')
checked expr   : trim<String>("  你  好  ")
optimized expr : "你  好"
output type    : String
output domain  : {"你  好"..="你  好"}
output         : '你  好'


ast            : trim('  분산 데이터베이스    ')
raw expr       : trim('  분산 데이터베이스    ')
checked expr   : trim<String>("  분산 데이터베이스    ")
optimized expr : "분산 데이터베이스"
output type    : String
output domain  : {"분산 데이터베이스"..="분산 데이터베이스"}
output         : '분산 데이터베이스'


ast            : trim('   あなたのことが好きです   ')
raw expr       : trim('   あなたのことが好きです   ')
checked expr   : trim<String>("   あなたのことが好きです   ")
optimized expr : "あなたのことが好きです"
output type    : String
output domain  : {"あなたのことが好きです"..="あなたのことが好きです"}
output         : 'あなたのことが好きです'


ast            : trim(NULL)
raw expr       : trim(NULL)
checked expr   : trim<String NULL>(CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : trim(a)
raw expr       : trim(a::String)
checked expr   : trim<String>(a)
evaluation:
+--------+-----------------------+--------+
|        | a                     | Output |
+--------+-----------------------+--------+
| Type   | String                | String |
| Domain | {"   abc"..="abc   "} | {""..} |
| Row 0  | 'abc'                 | 'abc'  |
| Row 1  | '   abc'              | 'abc'  |
| Row 2  | '   abc   '           | 'abc'  |
| Row 3  | 'abc   '              | 'abc'  |
+--------+-----------------------+--------+
evaluation (internal):
+--------+----------------------------------------------+
| Column | Data                                         |
+--------+----------------------------------------------+
| a      | StringColumn[abc,    abc,    abc   , abc   ] |
| Output | StringColumn[abc, abc, abc, abc]             |
+--------+----------------------------------------------+


ast            : trim('\ta\t')
raw expr       : trim('\ta\t')
checked expr   : trim<String>("\ta\t")
optimized expr : "\ta\t"
output type    : String
output domain  : {"\ta\t"..="\ta\t"}
output         : '\ta\t'


ast            : trim('*-*ABC-*-','*-')
raw expr       : trim('*-*ABC-*-', '*-')
checked expr   : trim<String, String>("*-*ABC-*-", "*-")
optimized expr : "ABC"
output type    : String
output domain  : {"ABC"..="ABC"}
output         : 'ABC'


ast            : trim(both 'a' from 'aaabbaaa')
raw expr       : trim_both('aaabbaaa', 'a')
checked expr   : trim_both<String, String>("aaabbaaa", "a")
optimized expr : "bb"
output type    : String
output domain  : {"bb"..="bb"}
output         : 'bb'


ast            : trim(both 'aa' from 'aaabbaaa')
raw expr       : trim_both('aaabbaaa', 'aa')
checked expr   : trim_both<String, String>("aaabbaaa", "aa")
optimized expr : "abba"
output type    : String
output domain  : {"abba"..="abba"}
output         : 'abba'


ast            : trim(both 'a' from 'aaaaaaaa')
raw expr       : trim_both('aaaaaaaa', 'a')
checked expr   : trim_both<String, String>("aaaaaaaa", "a")
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : trim(both 'b' from 'aaabbaaa')
raw expr       : trim_both('aaabbaaa', 'b')
checked expr   : trim_both<String, String>("aaabbaaa", "b")
optimized expr : "aaabbaaa"
output type    : String
output domain  : {"aaabbaaa"..="aaabbaaa"}
output         : 'aaabbaaa'


ast            : trim(both 'a' from NULL)
raw expr       : trim_both(NULL, 'a')
checked expr   : trim_both<String NULL, String NULL>(CAST<NULL>(NULL AS String NULL), CAST<String>("a" AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : trim(both NULL from 'aaaaaaaa')
raw expr       : trim_both('aaaaaaaa', NULL)
checked expr   : trim_both<String NULL, String NULL>(CAST<String>("aaaaaaaa" AS String NULL), CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : trim(both 'a' from a)
raw expr       : trim_both(a::String, 'a')
checked expr   : trim_both<String, String>(a, "a")
evaluation:
+--------+-----------------------+----------+
|        | a                     | Output   |
+--------+-----------------------+----------+
| Type   | String                | String   |
| Domain | {"aabbaa"..="ccddcc"} | {""..}   |
| Row 0  | 'aabbaa'              | 'bb'     |
| Row 1  | 'bbccbb'              | 'bbccbb' |
| Row 2  | 'ccddcc'              | 'ccddcc' |
+--------+-----------------------+----------+
evaluation (internal):
+--------+--------------------------------------+
| Column | Data                                 |
+--------+--------------------------------------+
| a      | StringColumn[aabbaa, bbccbb, ccddcc] |
| Output | StringColumn[bb, bbccbb, ccddcc]     |
+--------+--------------------------------------+


ast            : trim(both b from a)
raw expr       : trim_both(a::String, b::String)
checked expr   : trim_both<String, String>(a, b)
evaluation:
+--------+-----------------------+-------------+--------+
|        | a                     | b           | Output |
+--------+-----------------------+-------------+--------+
| Type   | String                | String      | String |
| Domain | {"aabbaa"..="ccddcc"} | {"a"..="c"} | {""..} |
| Row 0  | 'aabbaa'              | 'a'         | 'bb'   |
| Row 1  | 'bbccbb'              | 'b'         | 'cc'   |
| Row 2  | 'ccddcc'              | 'c'         | 'dd'   |
+--------+-----------------------+-------------+--------+
evaluation (internal):
+--------+--------------------------------------+
| Column | Data                                 |
+--------+--------------------------------------+
| a      | StringColumn[aabbaa, bbccbb, ccddcc] |
| b      | StringColumn[a, b, c]                |
| Output | StringColumn[bb, cc, dd]             |
+--------+--------------------------------------+


ast            : trim(both a from a)
raw expr       : trim_both(a::String, a::String)
checked expr   : trim_both<String, String>(a, a)
evaluation:
+--------+-----------------------+--------+
|        | a                     | Output |
+--------+-----------------------+--------+
| Type   | String                | String |
| Domain | {"aabbaa"..="ccddcc"} | {""..} |
| Row 0  | 'aabbaa'              | ''     |
| Row 1  | 'bbccbb'              | ''     |
| Row 2  | 'ccddcc'              | ''     |
+--------+-----------------------+--------+
evaluation (internal):
+--------+--------------------------------------+
| Column | Data                                 |
+--------+--------------------------------------+
| a      | StringColumn[aabbaa, bbccbb, ccddcc] |
| Output | StringColumn[, , ]                   |
+--------+--------------------------------------+


ast            : trim(both b from 'aba')
raw expr       : trim_both('aba', b::String)
checked expr   : trim_both<String, String>("aba", b)
evaluation:
+--------+-------------+--------+
|        | b           | Output |
+--------+-------------+--------+
| Type   | String      | String |
| Domain | {"a"..="c"} | {""..} |
| Row 0  | 'a'         | 'b'    |
| Row 1  | 'b'         | 'aba'  |
| Row 2  | 'c'         | 'aba'  |
+--------+-------------+--------+
evaluation (internal):
+--------+---------------------------+
| Column | Data                      |
+--------+---------------------------+
| b      | StringColumn[a, b, c]     |
| Output | StringColumn[b, aba, aba] |
+--------+---------------------------+


ast            : trim(leading 'a' from 'aaabbaaa')
raw expr       : trim_leading('aaabbaaa', 'a')
checked expr   : trim_leading<String, String>("aaabbaaa", "a")
optimized expr : "bbaaa"
output type    : String
output domain  : {"bbaaa"..="bbaaa"}
output         : 'bbaaa'


ast            : trim(leading 'aa' from 'aaabbaaa')
raw expr       : trim_leading('aaabbaaa', 'aa')
checked expr   : trim_leading<String, String>("aaabbaaa", "aa")
optimized expr : "abbaaa"
output type    : String
output domain  : {"abbaaa"..="abbaaa"}
output         : 'abbaaa'


ast            : trim(leading 'a' from 'aaaaaaaa')
raw expr       : trim_leading('aaaaaaaa', 'a')
checked expr   : trim_leading<String, String>("aaaaaaaa", "a")
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : trim(leading 'b' from 'aaabbaaa')
raw expr       : trim_leading('aaabbaaa', 'b')
checked expr   : trim_leading<String, String>("aaabbaaa", "b")
optimized expr : "aaabbaaa"
output type    : String
output domain  : {"aaabbaaa"..="aaabbaaa"}
output         : 'aaabbaaa'


ast            : trim(leading 'a' from NULL)
raw expr       : trim_leading(NULL, 'a')
checked expr   : trim_leading<String NULL, String NULL>(CAST<NULL>(NULL AS String NULL), CAST<String>("a" AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : trim(leading NULL from 'aaaaaaaa')
raw expr       : trim_leading('aaaaaaaa', NULL)
checked expr   : trim_leading<String NULL, String NULL>(CAST<String>("aaaaaaaa" AS String NULL), CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : trim(leading 'a' from a)
raw expr       : trim_leading(a::String, 'a')
checked expr   : trim_leading<String, String>(a, "a")
evaluation:
+--------+-----------------------+----------+
|        | a                     | Output   |
+--------+-----------------------+----------+
| Type   | String                | String   |
| Domain | {"aabbaa"..="ccddcc"} | {""..}   |
| Row 0  | 'aabbaa'              | 'bbaa'   |
| Row 1  | 'bbccbb'              | 'bbccbb' |
| Row 2  | 'ccddcc'              | 'ccddcc' |
+--------+-----------------------+----------+
evaluation (internal):
+--------+--------------------------------------+
| Column | Data                                 |
+--------+--------------------------------------+
| a      | StringColumn[aabbaa, bbccbb, ccddcc] |
| Output | StringColumn[bbaa, bbccbb, ccddcc]   |
+--------+--------------------------------------+


ast            : trim(leading b from a)
raw expr       : trim_leading(a::String, b::String)
checked expr   : trim_leading<String, String>(a, b)
evaluation:
+--------+-----------------------+-------------+--------+
|        | a                     | b           | Output |
+--------+-----------------------+-------------+--------+
| Type   | String                | String      | String |
| Domain | {"aabbaa"..="ccddcc"} | {"a"..="c"} | {""..} |
| Row 0  | 'aabbaa'              | 'a'         | 'bbaa' |
| Row 1  | 'bbccbb'              | 'b'         | 'ccbb' |
| Row 2  | 'ccddcc'              | 'c'         | 'ddcc' |
+--------+-----------------------+-------------+--------+
evaluation (internal):
+--------+--------------------------------------+
| Column | Data                                 |
+--------+--------------------------------------+
| a      | StringColumn[aabbaa, bbccbb, ccddcc] |
| b      | StringColumn[a, b, c]                |
| Output | StringColumn[bbaa, ccbb, ddcc]       |
+--------+--------------------------------------+


ast            : trim(leading a from a)
raw expr       : trim_leading(a::String, a::String)
checked expr   : trim_leading<String, String>(a, a)
evaluation:
+--------+-----------------------+--------+
|        | a                     | Output |
+--------+-----------------------+--------+
| Type   | String                | String |
| Domain | {"aabbaa"..="ccddcc"} | {""..} |
| Row 0  | 'aabbaa'              | ''     |
| Row 1  | 'bbccbb'              | ''     |
| Row 2  | 'ccddcc'              | ''     |
+--------+-----------------------+--------+
evaluation (internal):
+--------+--------------------------------------+
| Column | Data                                 |
+--------+--------------------------------------+
| a      | StringColumn[aabbaa, bbccbb, ccddcc] |
| Output | StringColumn[, , ]                   |
+--------+--------------------------------------+


ast            : trim(leading b from 'aba')
raw expr       : trim_leading('aba', b::String)
checked expr   : trim_leading<String, String>("aba", b)
evaluation:
+--------+-------------+--------+
|        | b           | Output |
+--------+-------------+--------+
| Type   | String      | String |
| Domain | {"a"..="c"} | {""..} |
| Row 0  | 'a'         | 'ba'   |
| Row 1  | 'b'         | 'aba'  |
| Row 2  | 'c'         | 'aba'  |
+--------+-------------+--------+
evaluation (internal):
+--------+----------------------------+
| Column | Data                       |
+--------+----------------------------+
| b      | StringColumn[a, b, c]      |
| Output | StringColumn[ba, aba, aba] |
+--------+----------------------------+


ast            : trim(trailing 'a' from 'aaabbaaa')
raw expr       : trim_trailing('aaabbaaa', 'a')
checked expr   : trim_trailing<String, String>("aaabbaaa", "a")
optimized expr : "aaabb"
output type    : String
output domain  : {"aaabb"..="aaabb"}
output         : 'aaabb'


ast            : trim(trailing 'aa' from 'aaabbaaa')
raw expr       : trim_trailing('aaabbaaa', 'aa')
checked expr   : trim_trailing<String, String>("aaabbaaa", "aa")
optimized expr : "aaabba"
output type    : String
output domain  : {"aaabba"..="aaabba"}
output         : 'aaabba'


ast            : trim(trailing 'a' from 'aaaaaaaa')
raw expr       : trim_trailing('aaaaaaaa', 'a')
checked expr   : trim_trailing<String, String>("aaaaaaaa", "a")
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : trim(trailing 'b' from 'aaabbaaa')
raw expr       : trim_trailing('aaabbaaa', 'b')
checked expr   : trim_trailing<String, String>("aaabbaaa", "b")
optimized expr : "aaabbaaa"
output type    : String
output domain  : {"aaabbaaa"..="aaabbaaa"}
output         : 'aaabbaaa'


ast            : trim(trailing 'a' from NULL)
raw expr       : trim_trailing(NULL, 'a')
checked expr   : trim_trailing<String NULL, String NULL>(CAST<NULL>(NULL AS String NULL), CAST<String>("a" AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : trim(trailing NULL from 'aaaaaaaa')
raw expr       : trim_trailing('aaaaaaaa', NULL)
checked expr   : trim_trailing<String NULL, String NULL>(CAST<String>("aaaaaaaa" AS String NULL), CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : trim(trailing 'a' from a)
raw expr       : trim_trailing(a::String, 'a')
checked expr   : trim_trailing<String, String>(a, "a")
evaluation:
+--------+-----------------------+----------+
|        | a                     | Output   |
+--------+-----------------------+----------+
| Type   | String                | String   |
| Domain | {"aabbaa"..="ccddcc"} | {""..}   |
| Row 0  | 'aabbaa'              | 'aabb'   |
| Row 1  | 'bbccbb'              | 'bbccbb' |
| Row 2  | 'ccddcc'              | 'ccddcc' |
+--------+-----------------------+----------+
evaluation (internal):
+--------+--------------------------------------+
| Column | Data                                 |
+--------+--------------------------------------+
| a      | StringColumn[aabbaa, bbccbb, ccddcc] |
| Output | StringColumn[aabb, bbccbb, ccddcc]   |
+--------+--------------------------------------+


ast            : trim(trailing b from a)
raw expr       : trim_trailing(a::String, b::String)
checked expr   : trim_trailing<String, String>(a, b)
evaluation:
+--------+-----------------------+-------------+--------+
|        | a                     | b           | Output |
+--------+-----------------------+-------------+--------+
| Type   | String                | String      | String |
| Domain | {"aabbaa"..="ccddcc"} | {"a"..="c"} | {""..} |
| Row 0  | 'aabbaa'              | 'a'         | 'aabb' |
| Row 1  | 'bbccbb'              | 'b'         | 'bbcc' |
| Row 2  | 'ccddcc'              | 'c'         | 'ccdd' |
+--------+-----------------------+-------------+--------+
evaluation (internal):
+--------+--------------------------------------+
| Column | Data                                 |
+--------+--------------------------------------+
| a      | StringColumn[aabbaa, bbccbb, ccddcc] |
| b      | StringColumn[a, b, c]                |
| Output | StringColumn[aabb, bbcc, ccdd]       |
+--------+--------------------------------------+


ast            : trim(trailing a from a)
raw expr       : trim_trailing(a::String, a::String)
checked expr   : trim_trailing<String, String>(a, a)
evaluation:
+--------+-----------------------+--------+
|        | a                     | Output |
+--------+-----------------------+--------+
| Type   | String                | String |
| Domain | {"aabbaa"..="ccddcc"} | {""..} |
| Row 0  | 'aabbaa'              | ''     |
| Row 1  | 'bbccbb'              | ''     |
| Row 2  | 'ccddcc'              | ''     |
+--------+-----------------------+--------+
evaluation (internal):
+--------+--------------------------------------+
| Column | Data                                 |
+--------+--------------------------------------+
| a      | StringColumn[aabbaa, bbccbb, ccddcc] |
| Output | StringColumn[, , ]                   |
+--------+--------------------------------------+


ast            : trim(trailing b from 'aba')
raw expr       : trim_trailing('aba', b::String)
checked expr   : trim_trailing<String, String>("aba", b)
evaluation:
+--------+-------------+--------+
|        | b           | Output |
+--------+-------------+--------+
| Type   | String      | String |
| Domain | {"a"..="c"} | {""..} |
| Row 0  | 'a'         | 'ab'   |
| Row 1  | 'b'         | 'aba'  |
| Row 2  | 'c'         | 'aba'  |
+--------+-------------+--------+
evaluation (internal):
+--------+----------------------------+
| Column | Data                       |
+--------+----------------------------+
| b      | StringColumn[a, b, c]      |
| Output | StringColumn[ab, aba, aba] |
+--------+----------------------------+


ast            : concat('5', 3, 4)
raw expr       : concat('5', 3, 4)
checked expr   : concat<String, String, String>("5", CAST<UInt8>(3_u8 AS String), CAST<UInt8>(4_u8 AS String))
optimized expr : "534"
output type    : String
output domain  : {"534"..="534"}
output         : '534'


ast            : concat('5', '3', '4')
raw expr       : concat('5', '3', '4')
checked expr   : concat<String, String, String>("5", "3", "4")
optimized expr : "534"
output type    : String
output domain  : {"534"..="534"}
output         : '534'


ast            : concat(NULL, '3', '4')
raw expr       : concat(NULL, '3', '4')
checked expr   : concat<String NULL, String NULL, String NULL>(CAST<NULL>(NULL AS String NULL), CAST<String>("3" AS String NULL), CAST<String>("4" AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : concat('忠犬ハチ公', 'CAFÉ', '数据库', 'قاعدة البيانات', 'НОЧЬ НА ОКРАИНЕ МОСКВЫ')
raw expr       : concat('忠犬ハチ公', 'CAFÉ', '数据库', 'قاعدة البيانات', 'НОЧЬ НА ОКРАИНЕ МОСКВЫ')
checked expr   : concat<String, String, String, String, String>("忠犬ハチ公", "CAFÉ", "数据库", "قاعدة البيانات", "НОЧЬ НА ОКРАИНЕ МОСКВЫ")
optimized expr : "忠犬ハチ公CAFÉ数据库قاعدة البياناتНОЧЬ НА ОКРАИНЕ МОСКВЫ"
output type    : String
output domain  : {"忠犬ハチ公CAFÉ数据库قاعدة البياناتНОЧЬ НА ОКРАИНЕ МОСКВЫ"..="忠犬ハチ公CAFÉ数据库قاعدة البياناتНОЧЬ НА ОКРАИНЕ МОСКВЫ"}
output         : '忠犬ハチ公CAFÉ数据库قاعدة البياناتНОЧЬ НА ОКРАИНЕ МОСКВЫ'


ast            : concat(a, '3', '4', '5')
raw expr       : concat(a::String, '3', '4', '5')
checked expr   : concat<String, String, String, String>(a, "3", "4", "5")
evaluation:
+--------+-----------------------+----------------+
|        | a                     | Output         |
+--------+-----------------------+----------------+
| Type   | String                | String         |
| Domain | {"   abc"..="abc   "} | {"   abc"..}   |
| Row 0  | 'abc'                 | 'abc345'       |
| Row 1  | '   abc'              | '   abc345'    |
| Row 2  | '   abc   '           | '   abc   345' |
| Row 3  | 'abc   '              | 'abc   345'    |
+--------+-----------------------+----------------+
evaluation (internal):
+--------+----------------------------------------------------------+
| Column | Data                                                     |
+--------+----------------------------------------------------------+
| a      | StringColumn[abc,    abc,    abc   , abc   ]             |
| Output | StringColumn[abc345,    abc345,    abc   345, abc   345] |
+--------+----------------------------------------------------------+


ast            : concat(a, '3')
raw expr       : concat(a::String NULL, '3')
checked expr   : concat<String NULL, String NULL>(a, CAST<String>("3" AS String NULL))
optimized expr : concat<String NULL, String NULL>(a, "3")
evaluation:
+--------+----------------------+------------------+
|        | a                    | Output           |
+--------+----------------------+------------------+
| Type   | String NULL          | String NULL      |
| Domain | {"a"..="d"} ∪ {NULL} | {"a"..} ∪ {NULL} |
| Row 0  | 'a'                  | 'a3'             |
| Row 1  | 'b'                  | 'b3'             |
| Row 2  | NULL                 | NULL             |
| Row 3  | 'd'                  | 'd3'             |
+--------+----------------------+------------------+
evaluation (internal):
+--------+---------------------------------------------------------------------------------+
| Column | Data                                                                            |
+--------+---------------------------------------------------------------------------------+
| a      | NullableColumn { column: StringColumn[a, b, c, d], validity: [0b____1011] }     |
| Output | NullableColumn { column: StringColumn[a3, b3, c3, d3], validity: [0b____1011] } |
+--------+---------------------------------------------------------------------------------+


ast            : concat_ws('-', '3', null, '4', null, '5')
raw expr       : concat_ws('-', '3', NULL, '4', NULL, '5')
checked expr   : concat_ws<String NULL, String NULL, String NULL, String NULL, String NULL, String NULL>(CAST<String>("-" AS String NULL), CAST<String>("3" AS String NULL), CAST<NULL>(NULL AS String NULL), CAST<String>("4" AS String NULL), CAST<NULL>(NULL AS String NULL), CAST<String>("5" AS String NULL))
optimized expr : "3-4-5"
output type    : String NULL
output domain  : {"3-4-5"..="3-4-5"}
output         : '3-4-5'


ast            : concat_ws(NULL, '3', '4')
raw expr       : concat_ws(NULL, '3', '4')
checked expr   : concat_ws<String NULL, String NULL, String NULL>(CAST<NULL>(NULL AS String NULL), CAST<String>("3" AS String NULL), CAST<String>("4" AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : concat_ws(',', '忠犬ハチ公', 'CAFÉ', '数据库', 'قاعدة البيانات', 'НОЧЬ НА ОКРАИНЕ МОСКВЫ')
raw expr       : concat_ws(',', '忠犬ハチ公', 'CAFÉ', '数据库', 'قاعدة البيانات', 'НОЧЬ НА ОКРАИНЕ МОСКВЫ')
checked expr   : concat_ws<String, String, String, String, String, String>(",", "忠犬ハチ公", "CAFÉ", "数据库", "قاعدة البيانات", "НОЧЬ НА ОКРАИНЕ МОСКВЫ")
optimized expr : "忠犬ハチ公,CAFÉ,数据库,قاعدة البيانات,НОЧЬ НА ОКРАИНЕ МОСКВЫ"
output type    : String
output domain  : {"忠犬ハチ公,CAFÉ,数据库,قاعدة البيانات,НОЧЬ НА ОКРАИНЕ МОСКВЫ"..="忠犬ハチ公,CAFÉ,数据库,قاعدة البيانات,НОЧЬ НА ОКРАИНЕ МОСКВЫ"}
output         : '忠犬ハチ公,CAFÉ,数据库,قاعدة البيانات,НОЧЬ НА ОКРАИНЕ МОСКВЫ'


ast            : concat_ws(a, '3', '4', '5')
raw expr       : concat_ws(a::String, '3', '4', '5')
checked expr   : concat_ws<String, String, String, String>(a, "3", "4", "5")
evaluation:
+--------+-------------+---------+
|        | a           | Output  |
+--------+-------------+---------+
| Type   | String      | String  |
| Domain | {","..="-"} | {"3"..} |
| Row 0  | ','         | '3,4,5' |
| Row 1  | '-'         | '3-4-5' |
| Row 2  | ','         | '3,4,5' |
| Row 3  | '-'         | '3-4-5' |
+--------+-------------+---------+
evaluation (internal):
+--------+------------------------------------------+
| Column | Data                                     |
+--------+------------------------------------------+
| a      | StringColumn[,, -, ,, -]                 |
| Output | StringColumn[3,4,5, 3-4-5, 3,4,5, 3-4-5] |
+--------+------------------------------------------+


ast            : concat_ws(a, '3')
raw expr       : concat_ws(a::String NULL, '3')
checked expr   : concat_ws<String NULL, String NULL>(a, CAST<String>("3" AS String NULL))
optimized expr : concat_ws<String NULL, String NULL>(a, "3")
evaluation:
+--------+----------------------+-----------------+
|        | a                    | Output          |
+--------+----------------------+-----------------+
| Type   | String NULL          | String NULL     |
| Domain | {"a"..="d"} ∪ {NULL} | {""..} ∪ {NULL} |
| Row 0  | 'a'                  | '3'             |
| Row 1  | 'b'                  | '3'             |
| Row 2  | NULL                 | NULL            |
| Row 3  | 'd'                  | '3'             |
+--------+----------------------+-----------------+
evaluation (internal):
+--------+-----------------------------------------------------------------------------+
| Column | Data                                                                        |
+--------+-----------------------------------------------------------------------------+
| a      | NullableColumn { column: StringColumn[a, b, c, d], validity: [0b____1011] } |
| Output | NullableColumn { column: StringColumn[3, 3, , 3], validity: [0b____1011] }  |
+--------+-----------------------------------------------------------------------------+


ast            : concat_ws(a, '3', '4')
raw expr       : concat_ws(a::String NULL, '3', '4')
checked expr   : concat_ws<String NULL, String NULL, String NULL>(a, CAST<String>("3" AS String NULL), CAST<String>("4" AS String NULL))
optimized expr : concat_ws<String NULL, String NULL, String NULL>(a, "3", "4")
evaluation:
+--------+----------------------+-----------------+
|        | a                    | Output          |
+--------+----------------------+-----------------+
| Type   | String NULL          | String NULL     |
| Domain | {"a"..="d"} ∪ {NULL} | {""..} ∪ {NULL} |
| Row 0  | 'a'                  | '3a4'           |
| Row 1  | 'b'                  | '3b4'           |
| Row 2  | NULL                 | NULL            |
| Row 3  | 'd'                  | '3d4'           |
+--------+----------------------+-----------------+
evaluation (internal):
+--------+----------------------------------------------------------------------------------+
| Column | Data                                                                             |
+--------+----------------------------------------------------------------------------------+
| a      | NullableColumn { column: StringColumn[a, b, c, d], validity: [0b____1011] }      |
| Output | NullableColumn { column: StringColumn[3a4, 3b4, , 3d4], validity: [0b____1011] } |
+--------+----------------------------------------------------------------------------------+


ast            : concat_ws('', a, 2)
raw expr       : concat_ws('', a::Boolean, 2)
checked expr   : concat_ws<String, String, String>("", CAST<Boolean>(a AS String), CAST<UInt8>(2_u8 AS String))
optimized expr : "false2"
evaluation:
+--------+---------+-----------------------+
|        | a       | Output                |
+--------+---------+-----------------------+
| Type   | Boolean | String                |
| Domain | {FALSE} | {"false2"..="false2"} |
| Row 0  | false   | 'false2'              |
| Row 1  | false   | 'false2'              |
| Row 2  | false   | 'false2'              |
+--------+---------+-----------------------+
evaluation (internal):
+--------+--------------------------------------+
| Column | Data                                 |
+--------+--------------------------------------+
| a      | Boolean([0b_____000])                |
| Output | StringColumn[false2, false2, false2] |
+--------+--------------------------------------+


ast            : concat_ws(NULL, a, 2)
raw expr       : concat_ws(NULL, a::Decimal(10, 0), 2)
checked expr   : concat_ws<String NULL, String NULL, String NULL>(CAST<NULL>(NULL AS String NULL), CAST<Decimal(10, 0)>(a AS String NULL), CAST<UInt8>(2_u8 AS String NULL))
optimized expr : concat_ws<String NULL, String NULL, String NULL>(NULL, CAST<Decimal(10, 0)>(a AS String NULL), "2")
output type    : String NULL
output domain  : {""..} ∪ {NULL}
output         : NULL


ast            : concat(4, a, 2)
raw expr       : concat(4, a::Decimal(10, 0), 2)
checked expr   : concat<String, String, String>(CAST<UInt8>(4_u8 AS String), CAST<Decimal(10, 0)>(a AS String), CAST<UInt8>(2_u8 AS String))
optimized expr : concat<String, String, String>("4", CAST<Decimal(10, 0)>(a AS String), "2")
evaluation:
+--------+----------------+---------+
|        | a              | Output  |
+--------+----------------+---------+
| Type   | Decimal(10, 0) | String  |
| Domain | {0..=2}        | {"4"..} |
| Row 0  | 0              | '402'   |
| Row 1  | 1              | '412'   |
| Row 2  | 2              | '422'   |
+--------+----------------+---------+
evaluation (internal):
+--------+-----------------------------+
| Column | Data                        |
+--------+-----------------------------+
| a      | Decimal128([0, 1, 2])       |
| Output | StringColumn[402, 412, 422] |
+--------+-----------------------------+


ast            : bin(a)
raw expr       : bin(a::Int8)
checked expr   : bin<Int64>(CAST<Int8>(a AS Int64))
evaluation:
+--------+----------+--------------------------------------------------------------------+
|        | a        | Output                                                             |
+--------+----------+--------------------------------------------------------------------+
| Type   | Int8     | String                                                             |
| Domain | {-1..=3} | {""..}                                                             |
| Row 0  | -1       | '1111111111111111111111111111111111111111111111111111111111111111' |
| Row 1  | 2        | '10'                                                               |
| Row 2  | 3        | '11'                                                               |
+--------+----------+--------------------------------------------------------------------+
evaluation (internal):
+--------+----------------------------------------------------------------------------------------+
| Column | Data                                                                                   |
+--------+----------------------------------------------------------------------------------------+
| a      | Int8([-1, 2, 3])                                                                       |
| Output | StringColumn[1111111111111111111111111111111111111111111111111111111111111111, 10, 11] |
+--------+----------------------------------------------------------------------------------------+


ast            : bin(a2)
raw expr       : bin(a2::UInt8 NULL)
checked expr   : bin<Int64 NULL>(CAST<UInt8 NULL>(a2 AS Int64 NULL))
evaluation:
+--------+------------------+-----------------+
|        | a2               | Output          |
+--------+------------------+-----------------+
| Type   | UInt8 NULL       | String NULL     |
| Domain | {1..=2} ∪ {NULL} | {""..} ∪ {NULL} |
| Row 0  | 1                | '1'             |
| Row 1  | 2                | '10'            |
| Row 2  | NULL             | NULL            |
+--------+------------------+-----------------+
evaluation (internal):
+--------+----------------------------------------------------------------------------+
| Column | Data                                                                       |
+--------+----------------------------------------------------------------------------+
| a2     | NullableColumn { column: UInt8([1, 2, 3]), validity: [0b_____011] }        |
| Output | NullableColumn { column: StringColumn[1, 10, 11], validity: [0b_____011] } |
+--------+----------------------------------------------------------------------------+


ast            : bin(b)
raw expr       : bin(b::Int16)
checked expr   : bin<Int64>(CAST<Int16>(b AS Int64))
evaluation:
+--------+---------+--------+
|        | b       | Output |
+--------+---------+--------+
| Type   | Int16   | String |
| Domain | {2..=6} | {""..} |
| Row 0  | 2       | '10'   |
| Row 1  | 4       | '100'  |
| Row 2  | 6       | '110'  |
+--------+---------+--------+
evaluation (internal):
+--------+----------------------------+
| Column | Data                       |
+--------+----------------------------+
| b      | Int16([2, 4, 6])           |
| Output | StringColumn[10, 100, 110] |
+--------+----------------------------+


ast            : bin(c)
raw expr       : bin(c::UInt32)
checked expr   : bin<Int64>(CAST<UInt32>(c AS Int64))
evaluation:
+--------+-----------+---------+
|        | c         | Output  |
+--------+-----------+---------+
| Type   | UInt32    | String  |
| Domain | {10..=30} | {""..}  |
| Row 0  | 10        | '1010'  |
| Row 1  | 20        | '10100' |
| Row 2  | 30        | '11110' |
+--------+-----------+---------+
evaluation (internal):
+--------+----------------------------------+
| Column | Data                             |
+--------+----------------------------------+
| c      | UInt32([10, 20, 30])             |
| Output | StringColumn[1010, 10100, 11110] |
+--------+----------------------------------+


error: 
  --> SQL:1:1
  |
1 | bin(d)
  | ^^^^^^ no function matches signature `bin(Float64)`, you might need to add explicit type casts.

candidate functions:
  bin(Int64) :: String            : unable to unify `Float64` with `Int64`
  bin(Int64 NULL) :: String NULL  : unable to unify `Float64` with `Int64`



error: 
  --> SQL:1:5
  |
1 | bin(e)
  |     ^ invalid digit found in string while evaluating function `to_int64('abc')` in expr `CAST(e AS Int64)`, during run expr: `bin(CAST(e AS Int64))`



ast            : oct(a)
raw expr       : oct(a::Int8)
checked expr   : oct<Int64>(CAST<Int8>(a AS Int64))
evaluation:
+--------+----------+--------------------------+
|        | a        | Output                   |
+--------+----------+--------------------------+
| Type   | Int8     | String                   |
| Domain | {-1..=3} | {""..}                   |
| Row 0  | -1       | '1777777777777777777777' |
| Row 1  | 2        | '2'                      |
| Row 2  | 3        | '3'                      |
+--------+----------+--------------------------+
evaluation (internal):
+--------+--------------------------------------------+
| Column | Data                                       |
+--------+--------------------------------------------+
| a      | Int8([-1, 2, 3])                           |
| Output | StringColumn[1777777777777777777777, 2, 3] |
+--------+--------------------------------------------+


ast            : oct(a2)
raw expr       : oct(a2::UInt8 NULL)
checked expr   : oct<Int64 NULL>(CAST<UInt8 NULL>(a2 AS Int64 NULL))
evaluation:
+--------+------------------+-----------------+
|        | a2               | Output          |
+--------+------------------+-----------------+
| Type   | UInt8 NULL       | String NULL     |
| Domain | {1..=2} ∪ {NULL} | {""..} ∪ {NULL} |
| Row 0  | 1                | '1'             |
| Row 1  | 2                | '2'             |
| Row 2  | NULL             | NULL            |
+--------+------------------+-----------------+
evaluation (internal):
+--------+--------------------------------------------------------------------------+
| Column | Data                                                                     |
+--------+--------------------------------------------------------------------------+
| a2     | NullableColumn { column: UInt8([1, 2, 3]), validity: [0b_____011] }      |
| Output | NullableColumn { column: StringColumn[1, 2, 3], validity: [0b_____011] } |
+--------+--------------------------------------------------------------------------+


ast            : oct(b)
raw expr       : oct(b::Int16)
checked expr   : oct<Int64>(CAST<Int16>(b AS Int64))
evaluation:
+--------+---------+--------+
|        | b       | Output |
+--------+---------+--------+
| Type   | Int16   | String |
| Domain | {2..=6} | {""..} |
| Row 0  | 2       | '2'    |
| Row 1  | 4       | '4'    |
| Row 2  | 6       | '6'    |
+--------+---------+--------+
evaluation (internal):
+--------+-----------------------+
| Column | Data                  |
+--------+-----------------------+
| b      | Int16([2, 4, 6])      |
| Output | StringColumn[2, 4, 6] |
+--------+-----------------------+


ast            : oct(c)
raw expr       : oct(c::UInt32)
checked expr   : oct<Int64>(CAST<UInt32>(c AS Int64))
evaluation:
+--------+-----------+--------+
|        | c         | Output |
+--------+-----------+--------+
| Type   | UInt32    | String |
| Domain | {10..=30} | {""..} |
| Row 0  | 10        | '12'   |
| Row 1  | 20        | '24'   |
| Row 2  | 30        | '36'   |
+--------+-----------+--------+
evaluation (internal):
+--------+--------------------------+
| Column | Data                     |
+--------+--------------------------+
| c      | UInt32([10, 20, 30])     |
| Output | StringColumn[12, 24, 36] |
+--------+--------------------------+


error: 
  --> SQL:1:1
  |
1 | oct(d)
  | ^^^^^^ no function matches signature `oct(Float64)`, you might need to add explicit type casts.

candidate functions:
  oct(Int64) :: String            : unable to unify `Float64` with `Int64`
  oct(Int64 NULL) :: String NULL  : unable to unify `Float64` with `Int64`



error: 
  --> SQL:1:5
  |
1 | oct(e)
  |     ^ invalid digit found in string while evaluating function `to_int64('abc')` in expr `CAST(e AS Int64)`, during run expr: `oct(CAST(e AS Int64))`



ast            : hex(a)
raw expr       : hex(a::Int8)
checked expr   : to_hex<Int64>(CAST<Int8>(a AS Int64))
evaluation:
+--------+----------+--------------------+
|        | a        | Output             |
+--------+----------+--------------------+
| Type   | Int8     | String             |
| Domain | {-1..=3} | {""..}             |
| Row 0  | -1       | 'ffffffffffffffff' |
| Row 1  | 2        | '2'                |
| Row 2  | 3        | '3'                |
+--------+----------+--------------------+
evaluation (internal):
+--------+--------------------------------------+
| Column | Data                                 |
+--------+--------------------------------------+
| a      | Int8([-1, 2, 3])                     |
| Output | StringColumn[ffffffffffffffff, 2, 3] |
+--------+--------------------------------------+


ast            : hex(a2)
raw expr       : hex(a2::UInt8 NULL)
checked expr   : to_hex<Int64 NULL>(CAST<UInt8 NULL>(a2 AS Int64 NULL))
evaluation:
+--------+------------------+-----------------+
|        | a2               | Output          |
+--------+------------------+-----------------+
| Type   | UInt8 NULL       | String NULL     |
| Domain | {1..=2} ∪ {NULL} | {""..} ∪ {NULL} |
| Row 0  | 1                | '1'             |
| Row 1  | 2                | '2'             |
| Row 2  | NULL             | NULL            |
+--------+------------------+-----------------+
evaluation (internal):
+--------+--------------------------------------------------------------------------+
| Column | Data                                                                     |
+--------+--------------------------------------------------------------------------+
| a2     | NullableColumn { column: UInt8([1, 2, 3]), validity: [0b_____011] }      |
| Output | NullableColumn { column: StringColumn[1, 2, 3], validity: [0b_____011] } |
+--------+--------------------------------------------------------------------------+


ast            : hex(b)
raw expr       : hex(b::Int16)
checked expr   : to_hex<Int64>(CAST<Int16>(b AS Int64))
evaluation:
+--------+---------+--------+
|        | b       | Output |
+--------+---------+--------+
| Type   | Int16   | String |
| Domain | {2..=6} | {""..} |
| Row 0  | 2       | '2'    |
| Row 1  | 4       | '4'    |
| Row 2  | 6       | '6'    |
+--------+---------+--------+
evaluation (internal):
+--------+-----------------------+
| Column | Data                  |
+--------+-----------------------+
| b      | Int16([2, 4, 6])      |
| Output | StringColumn[2, 4, 6] |
+--------+-----------------------+


ast            : hex(c)
raw expr       : hex(c::UInt32)
checked expr   : to_hex<Int64>(CAST<UInt32>(c AS Int64))
evaluation:
+--------+-----------+--------+
|        | c         | Output |
+--------+-----------+--------+
| Type   | UInt32    | String |
| Domain | {10..=30} | {""..} |
| Row 0  | 10        | 'a'    |
| Row 1  | 20        | '14'   |
| Row 2  | 30        | '1e'   |
+--------+-----------+--------+
evaluation (internal):
+--------+-------------------------+
| Column | Data                    |
+--------+-------------------------+
| c      | UInt32([10, 20, 30])    |
| Output | StringColumn[a, 14, 1e] |
+--------+-------------------------+


error: 
  --> SQL:1:1
  |
1 | hex(d)
  | ^^^^^^ no function matches signature `to_hex(Float64)`, you might need to add explicit type casts.

candidate functions:
  to_hex(String) :: String            : unable to unify `Float64` with `String`
  to_hex(String NULL) :: String NULL  : unable to unify `Float64` with `String`
  to_hex(Int64) :: String             : unable to unify `Float64` with `Int64`
... and 3 more



ast            : hex(e)
raw expr       : hex(e::String)
checked expr   : to_hex<String>(e)
evaluation:
+--------+-----------------+--------------------+
|        | e               | Output             |
+--------+-----------------+--------------------+
| Type   | String          | String             |
| Domain | {"abc"..="def"} | {""..}             |
| Row 0  | 'abc'           | '616263'           |
| Row 1  | 'def'           | '646566'           |
| Row 2  | 'databend'      | '6461746162656e64' |
+--------+-----------------+--------------------+
evaluation (internal):
+--------+------------------------------------------------+
| Column | Data                                           |
+--------+------------------------------------------------+
| e      | StringColumn[abc, def, databend]               |
| Output | StringColumn[616263, 646566, 6461746162656e64] |
+--------+------------------------------------------------+


ast            : lpad('hi', 2, '?')
raw expr       : lpad('hi', 2, '?')
checked expr   : lpad<String, UInt64, String>("hi", CAST<UInt8>(2_u8 AS UInt64), "?")
optimized expr : "hi"
output type    : String
output domain  : {"hi"..="hi"}
output         : 'hi'


ast            : lpad('hi', 4, '?')
raw expr       : lpad('hi', 4, '?')
checked expr   : lpad<String, UInt64, String>("hi", CAST<UInt8>(4_u8 AS UInt64), "?")
optimized expr : "??hi"
output type    : String
output domain  : {"??hi"..="??hi"}
output         : '??hi'


ast            : lpad('hi', 0, '?')
raw expr       : lpad('hi', 0, '?')
checked expr   : lpad<String, UInt64, String>("hi", CAST<UInt8>(0_u8 AS UInt64), "?")
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : lpad('hi', 1, '?')
raw expr       : lpad('hi', 1, '?')
checked expr   : lpad<String, UInt64, String>("hi", CAST<UInt8>(1_u8 AS UInt64), "?")
optimized expr : "h"
output type    : String
output domain  : {"h"..="h"}
output         : 'h'


error: 
  --> SQL:1:1
  |
1 | lpad('', 1, '')
  | ^^^^^^^^^^^^^^^ can't fill the '' length to '1' with an empty pad string while evaluating function `lpad('', 1, '')` in expr `lpad('', CAST(1 AS UInt64), '')`



ast            : lpad('hi', 1, '')
raw expr       : lpad('hi', 1, '')
checked expr   : lpad<String, UInt64, String>("hi", CAST<UInt8>(1_u8 AS UInt64), "")
optimized expr : "h"
output type    : String
output domain  : {"h"..="h"}
output         : 'h'


ast            : lpad('', 1, '?')
raw expr       : lpad('', 1, '?')
checked expr   : lpad<String, UInt64, String>("", CAST<UInt8>(1_u8 AS UInt64), "?")
optimized expr : "?"
output type    : String
output domain  : {"?"..="?"}
output         : '?'


error: 
  --> SQL:1:12
  |
1 | lpad('hi', -1, '?')
  |            ^ number overflowed while evaluating function `to_uint64(-1)` in expr `CAST(- 1 AS UInt64)`, during run expr: `lpad('hi', CAST(- 1 AS UInt64), '?')`



error: 
  --> SQL:1:1
  |
1 | lpad('hi', 2000000, '?')
  | ^^^^^^^^^^^^^^^^^^^^^^^^ padding length '2000000' is too big, max is: '1000000' while evaluating function `lpad('hi', 2000000, '?')` in expr `lpad('hi', CAST(2000000 AS UInt64), '?')`



ast            : lpad(a, b, c)
raw expr       : lpad(a::String, b::UInt8, c::String)
checked expr   : lpad<String, UInt64, String>(a, CAST<UInt8>(b AS UInt64), c)
evaluation:
+--------+-----------------+---------+-------------+---------+
|        | a               | b       | c           | Output  |
+--------+-----------------+---------+-------------+---------+
| Type   | String          | UInt8   | String      | String  |
| Domain | {"cc"..="test"} | {0..=5} | {"?"..="x"} | Unknown |
| Row 0  | 'hi'            | 0       | '?'         | ''      |
| Row 1  | 'test'          | 3       | 'x'         | 'tes'   |
| Row 2  | 'cc'            | 5       | 'bb'        | 'bbbcc' |
+--------+-----------------+---------+-------------+---------+
evaluation (internal):
+--------+----------------------------+
| Column | Data                       |
+--------+----------------------------+
| a      | StringColumn[hi, test, cc] |
| b      | UInt8([0, 3, 5])           |
| c      | StringColumn[?, x, bb]     |
| Output | StringColumn[, tes, bbbcc] |
+--------+----------------------------+


error: 
  --> SQL:1:1
  |
1 | lpad(a, b, c)
  | ^^^^^^^^^^^^^ can't fill the 'hi' length to '5' with an empty pad string while evaluating function `lpad('hi', 5, '')` in expr `lpad(a, CAST(b AS UInt64), c)`



ast            : rpad('hi', 2, '?')
raw expr       : rpad('hi', 2, '?')
checked expr   : rpad<String, UInt64, String>("hi", CAST<UInt8>(2_u8 AS UInt64), "?")
optimized expr : "hi"
output type    : String
output domain  : {"hi"..="hi"}
output         : 'hi'


ast            : rpad('hi', 4, '?')
raw expr       : rpad('hi', 4, '?')
checked expr   : rpad<String, UInt64, String>("hi", CAST<UInt8>(4_u8 AS UInt64), "?")
optimized expr : "hi??"
output type    : String
output domain  : {"hi??"..="hi??"}
output         : 'hi??'


ast            : rpad('hi', 0, '?')
raw expr       : rpad('hi', 0, '?')
checked expr   : rpad<String, UInt64, String>("hi", CAST<UInt8>(0_u8 AS UInt64), "?")
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : rpad('hi', 1, '?')
raw expr       : rpad('hi', 1, '?')
checked expr   : rpad<String, UInt64, String>("hi", CAST<UInt8>(1_u8 AS UInt64), "?")
optimized expr : "h"
output type    : String
output domain  : {"h"..="h"}
output         : 'h'


error: 
  --> SQL:1:1
  |
1 | rpad('', 1, '')
  | ^^^^^^^^^^^^^^^ can't fill the '' length to '1' with an empty pad string while evaluating function `rpad('', 1, '')` in expr `rpad('', CAST(1 AS UInt64), '')`



ast            : rpad('hi', 1, '')
raw expr       : rpad('hi', 1, '')
checked expr   : rpad<String, UInt64, String>("hi", CAST<UInt8>(1_u8 AS UInt64), "")
optimized expr : "h"
output type    : String
output domain  : {"h"..="h"}
output         : 'h'


ast            : rpad('', 1, '?')
raw expr       : rpad('', 1, '?')
checked expr   : rpad<String, UInt64, String>("", CAST<UInt8>(1_u8 AS UInt64), "?")
optimized expr : "?"
output type    : String
output domain  : {"?"..="?"}
output         : '?'


error: 
  --> SQL:1:12
  |
1 | rpad('hi', -1, '?')
  |            ^ number overflowed while evaluating function `to_uint64(-1)` in expr `CAST(- 1 AS UInt64)`, during run expr: `rpad('hi', CAST(- 1 AS UInt64), '?')`



error: 
  --> SQL:1:1
  |
1 | rpad('hi', 2000000, '?')
  | ^^^^^^^^^^^^^^^^^^^^^^^^ padding length '2000000' is too big, max is: '1000000' while evaluating function `rpad('hi', 2000000, '?')` in expr `rpad('hi', CAST(2000000 AS UInt64), '?')`



ast            : rpad(a, b, c)
raw expr       : rpad(a::String, b::UInt8, c::String)
checked expr   : rpad<String, UInt64, String>(a, CAST<UInt8>(b AS UInt64), c)
evaluation:
+--------+-----------------+---------+-------------+---------+
|        | a               | b       | c           | Output  |
+--------+-----------------+---------+-------------+---------+
| Type   | String          | UInt8   | String      | String  |
| Domain | {"cc"..="test"} | {0..=5} | {"?"..="x"} | Unknown |
| Row 0  | 'hi'            | 0       | '?'         | ''      |
| Row 1  | 'test'          | 3       | 'x'         | 'tes'   |
| Row 2  | 'cc'            | 5       | 'bb'        | 'ccbbb' |
+--------+-----------------+---------+-------------+---------+
evaluation (internal):
+--------+----------------------------+
| Column | Data                       |
+--------+----------------------------+
| a      | StringColumn[hi, test, cc] |
| b      | UInt8([0, 3, 5])           |
| c      | StringColumn[?, x, bb]     |
| Output | StringColumn[, tes, ccbbb] |
+--------+----------------------------+


error: 
  --> SQL:1:1
  |
1 | rpad(a, b, c)
  | ^^^^^^^^^^^^^ can't fill the 'hi' length to '5' with an empty pad string while evaluating function `rpad('hi', 5, '')` in expr `rpad(a, CAST(b AS UInt64), c)`



ast            : replace('hi', '', '?')
raw expr       : replace('hi', '', '?')
checked expr   : replace<String, String, String>("hi", "", "?")
optimized expr : "hi"
output type    : String
output domain  : {"hi"..="hi"}
output         : 'hi'


ast            : replace('hi', '', 'hi')
raw expr       : replace('hi', '', 'hi')
checked expr   : replace<String, String, String>("hi", "", "hi")
optimized expr : "hi"
output type    : String
output domain  : {"hi"..="hi"}
output         : 'hi'


ast            : replace('hi', 'i', '?')
raw expr       : replace('hi', 'i', '?')
checked expr   : replace<String, String, String>("hi", "i", "?")
optimized expr : "h?"
output type    : String
output domain  : {"h?"..="h?"}
output         : 'h?'


ast            : replace('hi', 'x', '?')
raw expr       : replace('hi', 'x', '?')
checked expr   : replace<String, String, String>("hi", "x", "?")
optimized expr : "hi"
output type    : String
output domain  : {"hi"..="hi"}
output         : 'hi'


ast            : replace(a, b, c)
raw expr       : replace(a::String, b::String, c::String)
checked expr   : replace<String, String, String>(a, b, c)
evaluation:
+--------+-----------------+-------------+-------------+--------+
|        | a               | b           | c           | Output |
+--------+-----------------+-------------+-------------+--------+
| Type   | String          | String      | String      | String |
| Domain | {"cc"..="test"} | {""..="te"} | {"?"..="x"} | {""..} |
| Row 0  | 'hi'            | 'i'         | '?'         | 'h?'   |
| Row 1  | 'test'          | 'te'        | 'x'         | 'xst'  |
| Row 2  | 'cc'            | 'cc'        | 'bb'        | 'bb'   |
| Row 3  | 'q'             | ''          | 'q'         | 'q'    |
+--------+-----------------+-------------+-------------+--------+
evaluation (internal):
+--------+-------------------------------+
| Column | Data                          |
+--------+-------------------------------+
| a      | StringColumn[hi, test, cc, q] |
| b      | StringColumn[i, te, cc, ]     |
| c      | StringColumn[?, x, bb, q]     |
| Output | StringColumn[h?, xst, bb, q]  |
+--------+-------------------------------+


ast            : translate('abcdefabcdef', 'dc', 'zy')
raw expr       : translate('abcdefabcdef', 'dc', 'zy')
checked expr   : translate<String, String, String>("abcdefabcdef", "dc", "zy")
optimized expr : "abyzefabyzef"
output type    : String
output domain  : {"abyzefabyzef"..="abyzefabyzef"}
output         : 'abyzefabyzef'


ast            : translate('abcdefabcdef', '', 'zy')
raw expr       : translate('abcdefabcdef', '', 'zy')
checked expr   : translate<String, String, String>("abcdefabcdef", "", "zy")
optimized expr : "abcdefabcdef"
output type    : String
output domain  : {"abcdefabcdef"..="abcdefabcdef"}
output         : 'abcdefabcdef'


ast            : translate('abcdefabcdef', 'dc', '')
raw expr       : translate('abcdefabcdef', 'dc', '')
checked expr   : translate<String, String, String>("abcdefabcdef", "dc", "")
optimized expr : "abefabef"
output type    : String
output domain  : {"abefabef"..="abefabef"}
output         : 'abefabef'


ast            : translate('abcdefabcdef', 'dc', 'dc')
raw expr       : translate('abcdefabcdef', 'dc', 'dc')
checked expr   : translate<String, String, String>("abcdefabcdef", "dc", "dc")
optimized expr : "abcdefabcdef"
output type    : String
output domain  : {"abcdefabcdef"..="abcdefabcdef"}
output         : 'abcdefabcdef'


ast            : translate(a, b, c)
raw expr       : translate(a::String, b::String, c::String)
checked expr   : translate<String, String, String>(a, b, c)
optimized expr : translate<String, String, String>("abcdef", b, c)
evaluation:
+--------+-----------------------+-------------+-------------+----------+
|        | a                     | b           | c           | Output   |
+--------+-----------------------+-------------+-------------+----------+
| Type   | String                | String      | String      | String   |
| Domain | {"abcdef"..="abcdef"} | {""..="dc"} | {""..="zy"} | {""..}   |
| Row 0  | 'abcdef'              | 'dc'        | 'zy'        | 'abyzef' |
| Row 1  | 'abcdef'              | ''          | 'zy'        | 'abcdef' |
| Row 2  | 'abcdef'              | 'dc'        | ''          | 'abef'   |
| Row 3  | 'abcdef'              | 'dc'        | 'dc'        | 'abcdef' |
+--------+-----------------------+-------------+-------------+----------+
evaluation (internal):
+--------+----------------------------------------------+
| Column | Data                                         |
+--------+----------------------------------------------+
| a      | StringColumn[abcdef, abcdef, abcdef, abcdef] |
| b      | StringColumn[dc, , dc, dc]                   |
| c      | StringColumn[zy, zy, , dc]                   |
| Output | StringColumn[abyzef, abcdef, abef, abcdef]   |
+--------+----------------------------------------------+


ast            : strcmp('text', 'text2')
raw expr       : strcmp('text', 'text2')
checked expr   : strcmp<String, String>("text", "text2")
optimized expr : -1_i8
output type    : Int8
output domain  : {-1..=-1}
output         : -1


ast            : strcmp('text2', 'text')
raw expr       : strcmp('text2', 'text')
checked expr   : strcmp<String, String>("text2", "text")
optimized expr : 1_i8
output type    : Int8
output domain  : {1..=1}
output         : 1


ast            : strcmp('hii', 'hii')
raw expr       : strcmp('hii', 'hii')
checked expr   : strcmp<String, String>("hii", "hii")
optimized expr : 0_i8
output type    : Int8
output domain  : {0..=0}
output         : 0


ast            : strcmp(a, b)
raw expr       : strcmp(a::String, b::String)
checked expr   : strcmp<String, String>(a, b)
evaluation:
+--------+-----------------+------------------+--------------+
|        | a               | b                | Output       |
+--------+-----------------+------------------+--------------+
| Type   | String          | String           | Int8         |
| Domain | {"cc"..="test"} | {"ccb"..="test"} | {-128..=127} |
| Row 0  | 'i'             | 'hi'             | 1            |
| Row 1  | 'h'             | 'hi'             | -1           |
| Row 2  | 'test'          | 'test'           | 0            |
| Row 3  | 'cc'            | 'ccb'            | -1           |
+--------+-----------------+------------------+--------------+
evaluation (internal):
+--------+---------------------------------+
| Column | Data                            |
+--------+---------------------------------+
| a      | StringColumn[i, h, test, cc]    |
| b      | StringColumn[hi, hi, test, ccb] |
| Output | Int8([1, -1, 0, -1])            |
+--------+---------------------------------+


ast            : locate('bar', 'foobarbar')
raw expr       : locate('bar', 'foobarbar')
checked expr   : locate<String, String>("bar", "foobarbar")
optimized expr : 4_u64
output type    : UInt64
output domain  : {4..=4}
output         : 4


ast            : locate('', 'foobarbar')
raw expr       : locate('', 'foobarbar')
checked expr   : locate<String, String>("", "foobarbar")
optimized expr : 1_u64
output type    : UInt64
output domain  : {1..=1}
output         : 1


ast            : locate('', '')
raw expr       : locate('', '')
checked expr   : locate<String, String>("", "")
optimized expr : 1_u64
output type    : UInt64
output domain  : {1..=1}
output         : 1


ast            : locate('好世', '你好世界')
raw expr       : locate('好世', '你好世界')
checked expr   : locate<String, String>("好世", "你好世界")
optimized expr : 2_u64
output type    : UInt64
output domain  : {2..=2}
output         : 2


ast            : instr('foobarbar', 'bar')
raw expr       : instr('foobarbar', 'bar')
checked expr   : instr<String, String>("foobarbar", "bar")
optimized expr : 4_u64
output type    : UInt64
output domain  : {4..=4}
output         : 4


ast            : instr('foobarbar', '')
raw expr       : instr('foobarbar', '')
checked expr   : instr<String, String>("foobarbar", "")
optimized expr : 1_u64
output type    : UInt64
output domain  : {1..=1}
output         : 1


ast            : instr('', '')
raw expr       : instr('', '')
checked expr   : instr<String, String>("", "")
optimized expr : 1_u64
output type    : UInt64
output domain  : {1..=1}
output         : 1


ast            : position('bar' IN 'foobarbar')
raw expr       : position('bar', 'foobarbar')
checked expr   : position<String, String>("bar", "foobarbar")
optimized expr : 4_u64
output type    : UInt64
output domain  : {4..=4}
output         : 4


ast            : position('' IN 'foobarbar')
raw expr       : position('', 'foobarbar')
checked expr   : position<String, String>("", "foobarbar")
optimized expr : 1_u64
output type    : UInt64
output domain  : {1..=1}
output         : 1


ast            : position('' IN '')
raw expr       : position('', '')
checked expr   : position<String, String>("", "")
optimized expr : 1_u64
output type    : UInt64
output domain  : {1..=1}
output         : 1


ast            : position('foobarbar' IN 'bar')
raw expr       : position('foobarbar', 'bar')
checked expr   : position<String, String>("foobarbar", "bar")
optimized expr : 0_u64
output type    : UInt64
output domain  : {0..=0}
output         : 0


ast            : locate('bar', 'foobarbar', 5)
raw expr       : locate('bar', 'foobarbar', 5)
checked expr   : locate<String, String, UInt64>("bar", "foobarbar", CAST<UInt8>(5_u8 AS UInt64))
optimized expr : 7_u64
output type    : UInt64
output domain  : {7..=7}
output         : 7


ast            : locate('好世', '你好世界', 1)
raw expr       : locate('好世', '你好世界', 1)
checked expr   : locate<String, String, UInt64>("好世", "你好世界", CAST<UInt8>(1_u8 AS UInt64))
optimized expr : 2_u64
output type    : UInt64
output domain  : {2..=2}
output         : 2


ast            : locate(a, b, c)
raw expr       : locate(a::String, b::String, c::UInt8)
checked expr   : locate<String, String, UInt64>(a, b, CAST<UInt8>(c AS UInt64))
evaluation:
+--------+---------------+---------------+---------+----------------------------+
|        | a             | b             | c       | Output                     |
+--------+---------------+---------------+---------+----------------------------+
| Type   | String        | String        | UInt8   | UInt64                     |
| Domain | {"bar"..="q"} | {"56"..="xx"} | {0..=2} | {0..=18446744073709551615} |
| Row 0  | 'bar'         | 'foobarbar'   | 1       | 4                          |
| Row 1  | 'cc'          | 'bdccacc'     | 2       | 3                          |
| Row 2  | 'cc'          | 'xx'          | 0       | 0                          |
| Row 3  | 'q'           | '56'          | 1       | 0                          |
+--------+---------------+---------------+---------+----------------------------+
evaluation (internal):
+--------+------------------------------------------+
| Column | Data                                     |
+--------+------------------------------------------+
| a      | StringColumn[bar, cc, cc, q]             |
| b      | StringColumn[foobarbar, bdccacc, xx, 56] |
| c      | UInt8([1, 2, 0, 1])                      |
| Output | UInt64([4, 3, 0, 0])                     |
+--------+------------------------------------------+


ast            : char(65,66,67)
raw expr       : char(65, 66, 67)
checked expr   : char<Int64, Int64, Int64>(CAST<UInt8>(65_u8 AS Int64), CAST<UInt8>(66_u8 AS Int64), CAST<UInt8>(67_u8 AS Int64))
optimized expr : "ABC"
output type    : String
output domain  : {"ABC"..="ABC"}
output         : 'ABC'


ast            : char(65, null)
raw expr       : char(65, NULL)
checked expr   : char<Int64 NULL, Int64 NULL>(CAST<UInt8>(65_u8 AS Int64 NULL), CAST<NULL>(NULL AS Int64 NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : char(a, b, c)
raw expr       : char(a::UInt8, b::UInt8, c::UInt8)
checked expr   : char<Int64, Int64, Int64>(CAST<UInt8>(a AS Int64), CAST<UInt8>(b AS Int64), CAST<UInt8>(c AS Int64))
evaluation:
+--------+-----------+-----------+-----------+--------+
|        | a         | b         | c         | Output |
+--------+-----------+-----------+-----------+--------+
| Type   | UInt8     | UInt8     | UInt8     | String |
| Domain | {66..=67} | {98..=99} | {68..=69} | {""..} |
| Row 0  | 66        | 98        | 68        | 'BbD'  |
| Row 1  | 67        | 99        | 69        | 'CcE'  |
+--------+-----------+-----------+-----------+--------+
evaluation (internal):
+--------+------------------------+
| Column | Data                   |
+--------+------------------------+
| a      | UInt8([66, 67])        |
| b      | UInt8([98, 99])        |
| c      | UInt8([68, 69])        |
| Output | StringColumn[BbD, CcE] |
+--------+------------------------+


ast            : char(a2, b, c)
raw expr       : char(a2::UInt8 NULL, b::UInt8, c::UInt8)
checked expr   : char<Int64 NULL, Int64 NULL, Int64 NULL>(CAST<UInt8 NULL>(a2 AS Int64 NULL), CAST<UInt8>(b AS Int64 NULL), CAST<UInt8>(c AS Int64 NULL))
evaluation:
+--------+-----------+-----------+--------------------+-----------------+
|        | b         | c         | a2                 | Output          |
+--------+-----------+-----------+--------------------+-----------------+
| Type   | UInt8     | UInt8     | UInt8 NULL         | String NULL     |
| Domain | {98..=99} | {68..=69} | {66..=66} ∪ {NULL} | {""..} ∪ {NULL} |
| Row 0  | 98        | 68        | 66                 | 'BbD'           |
| Row 1  | 99        | 69        | NULL               | NULL            |
+--------+-----------+-----------+--------------------+-----------------+
evaluation (internal):
+--------+---------------------------------------------------------------------------+
| Column | Data                                                                      |
+--------+---------------------------------------------------------------------------+
| b      | UInt8([98, 99])                                                           |
| c      | UInt8([68, 69])                                                           |
| a2     | NullableColumn { column: UInt8([66, 67]), validity: [0b______01] }        |
| Output | NullableColumn { column: StringColumn[BbD, CcE], validity: [0b______01] } |
+--------+---------------------------------------------------------------------------+


ast            : char(c2)
raw expr       : char(c2::UInt16)
checked expr   : char<Int64>(CAST<UInt16>(c2 AS Int64))
evaluation:
+--------+-----------+--------+
|        | c2        | Output |
+--------+-----------+--------+
| Type   | UInt16    | String |
| Domain | {68..=69} | {""..} |
| Row 0  | 68        | 'D'    |
| Row 1  | 69        | 'E'    |
+--------+-----------+--------+
evaluation (internal):
+--------+--------------------+
| Column | Data               |
+--------+--------------------+
| c2     | UInt16([68, 69])   |
| Output | StringColumn[D, E] |
+--------+--------------------+


ast            : soundex('你好中国北京')
raw expr       : soundex('你好中国北京')
checked expr   : soundex<String>("你好中国北京")
optimized expr : "你000"
output type    : String
output domain  : {"你000"..="你000"}
output         : '你000'


ast            : soundex('')
raw expr       : soundex('')
checked expr   : soundex<String>("")
optimized expr : "0000"
output type    : String
output domain  : {"0000"..="0000"}
output         : '0000'


ast            : soundex('hello all folks')
raw expr       : soundex('hello all folks')
checked expr   : soundex<String>("hello all folks")
optimized expr : "H4142"
output type    : String
output domain  : {"H4142"..="H4142"}
output         : 'H4142'


ast            : soundex('#3556 in bugdb')
raw expr       : soundex('#3556 in bugdb')
checked expr   : soundex<String>("#3556 in bugdb")
optimized expr : "I51231"
output type    : String
output domain  : {"I51231"..="I51231"}
output         : 'I51231'


ast            : soundex(a)
raw expr       : soundex(a::String)
checked expr   : soundex<String>(a)
evaluation:
+--------+------------------------------------+---------+
|        | a                                  | Output  |
+--------+------------------------------------+---------+
| Type   | String                             | String  |
| Domain | {"#🐑🐑he🐑llo🐑"..="🐑he🐑llo🐑"} | {""..}  |
| Row 0  | '#🐑🐑he🐑llo🐑'                   | '🐑400' |
| Row 1  | '🐑he🐑llo🐑'                      | '🐑400' |
| Row 2  | 'teacher'                          | 'T260'  |
| Row 3  | 'TEACHER'                          | 'T260'  |
+--------+------------------------------------+---------+
evaluation (internal):
+--------+-------------------------------------------------------------+
| Column | Data                                                        |
+--------+-------------------------------------------------------------+
| a      | StringColumn[#🐑🐑he🐑llo🐑, 🐑he🐑llo🐑, teacher, TEACHER] |
| Output | StringColumn[🐑400, 🐑400, T260, T260]                      |
+--------+-------------------------------------------------------------+


ast            : ord(NULL)
raw expr       : ord(NULL)
checked expr   : ord<String NULL>(CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : UInt64 NULL
output domain  : {NULL}
output         : NULL


ast            : ord('и')
raw expr       : ord('и')
checked expr   : ord<String>("и")
optimized expr : 53432_u64
output type    : UInt64
output domain  : {53432..=53432}
output         : 53432


ast            : ord('早ab')
raw expr       : ord('早ab')
checked expr   : ord<String>("早ab")
optimized expr : 15112105_u64
output type    : UInt64
output domain  : {15112105..=15112105}
output         : 15112105


ast            : ord('💖')
raw expr       : ord('💖')
checked expr   : ord<String>("💖")
optimized expr : 4036989590_u64
output type    : UInt64
output domain  : {4036989590..=4036989590}
output         : 4036989590


ast            : repeat('3', NULL)
raw expr       : repeat('3', NULL)
checked expr   : repeat<String NULL, UInt64 NULL>(CAST<String>("3" AS String NULL), CAST<NULL>(NULL AS UInt64 NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : repeat('3', 5)
raw expr       : repeat('3', 5)
checked expr   : repeat<String, UInt64>("3", CAST<UInt8>(5_u8 AS UInt64))
optimized expr : "33333"
output type    : String
output domain  : {"33333"..="33333"}
output         : '33333'


ast            : repeat('你好世界', 3)
raw expr       : repeat('你好世界', 3)
checked expr   : repeat<String, UInt64>("你好世界", CAST<UInt8>(3_u8 AS UInt64))
optimized expr : "你好世界你好世界你好世界"
output type    : String
output domain  : {"你好世界你好世界你好世界"..="你好世界你好世界你好世界"}
output         : '你好世界你好世界你好世界'


ast            : repeat('こんにちは', 2)
raw expr       : repeat('こんにちは', 2)
checked expr   : repeat<String, UInt64>("こんにちは", CAST<UInt8>(2_u8 AS UInt64))
optimized expr : "こんにちはこんにちは"
output type    : String
output domain  : {"こんにちはこんにちは"..="こんにちはこんにちは"}
output         : 'こんにちはこんにちは'


error: 
  --> SQL:1:1
  |
1 | repeat('3', 1000001)
  | ^^^^^^^^^^^^^^^^^^^^ Too many times to repeat: (1000001), maximum is: 1000000 while evaluating function `repeat('3', 1000001)` in expr `repeat('3', CAST(1000001 AS UInt64))`



ast            : repeat(a, 3)
raw expr       : repeat(a::String, 3)
checked expr   : repeat<String, UInt64>(a, CAST<UInt8>(3_u8 AS UInt64))
optimized expr : repeat<String, UInt64>(a, 3_u64)
evaluation:
+--------+-------------+---------+
|        | a           | Output  |
+--------+-------------+---------+
| Type   | String      | String  |
| Domain | {"a"..="c"} | Unknown |
| Row 0  | 'a'         | 'aaa'   |
| Row 1  | 'b'         | 'bbb'   |
| Row 2  | 'c'         | 'ccc'   |
+--------+-------------+---------+
evaluation (internal):
+--------+-----------------------------+
| Column | Data                        |
+--------+-----------------------------+
| a      | StringColumn[a, b, c]       |
| Output | StringColumn[aaa, bbb, ccc] |
+--------+-----------------------------+


error: 
  --> SQL:1:1
  |
1 | insert('Quadratic', 3, 4, 'What', 4)
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ no function matches signature `insert(String, UInt8, UInt8, String, UInt8)`, you might need to add explicit type casts.



error: 
  --> SQL:1:1
  |
1 | insert('Quadratic', 3, 4)
  | ^^^^^^^^^^^^^^^^^^^^^^^^^ no function matches signature `insert(String, UInt8, UInt8)`, you might need to add explicit type casts.



ast            : insert('Quadratic', 3, 4, a)
raw expr       : insert('Quadratic', 3, 4, a::String)
checked expr   : insert<String, Int64, Int64, String>("Quadratic", CAST<UInt8>(3_u8 AS Int64), CAST<UInt8>(4_u8 AS Int64), a)
optimized expr : insert<String, Int64, Int64, String>("Quadratic", 3_i64, 4_i64, a)
evaluation:
+--------+-------------------+-------------+
|        | a                 | Output      |
+--------+-------------------+-------------+
| Type   | String            | String      |
| Domain | {"fuck"..="what"} | {""..}      |
| Row 0  | 'what'            | 'Quwhattic' |
| Row 1  | 'the'             | 'Quthetic'  |
| Row 2  | 'fuck'            | 'Qufucktic' |
+--------+-------------------+-------------+
evaluation (internal):
+--------+----------------------------------------------+
| Column | Data                                         |
+--------+----------------------------------------------+
| a      | StringColumn[what, the, fuck]                |
| Output | StringColumn[Quwhattic, Quthetic, Qufucktic] |
+--------+----------------------------------------------+


ast            : insert('Quadratic', 3, 4, 'What')
raw expr       : insert('Quadratic', 3, 4, 'What')
checked expr   : insert<String, Int64, Int64, String>("Quadratic", CAST<UInt8>(3_u8 AS Int64), CAST<UInt8>(4_u8 AS Int64), "What")
optimized expr : "QuWhattic"
output type    : String
output domain  : {"QuWhattic"..="QuWhattic"}
output         : 'QuWhattic'


ast            : insert('Quadratic', -1, 4, 'What')
raw expr       : insert('Quadratic', minus(1), 4, 'What')
checked expr   : insert<String, Int64, Int64, String>("Quadratic", CAST<Int16>(minus<UInt8>(1_u8) AS Int64), CAST<UInt8>(4_u8 AS Int64), "What")
optimized expr : "Quadratic"
output type    : String
output domain  : {"Quadratic"..="Quadratic"}
output         : 'Quadratic'


ast            : insert('Quadratic', 3, 100, 'What')
raw expr       : insert('Quadratic', 3, 100, 'What')
checked expr   : insert<String, Int64, Int64, String>("Quadratic", CAST<UInt8>(3_u8 AS Int64), CAST<UInt8>(100_u8 AS Int64), "What")
optimized expr : "QuWhat"
output type    : String
output domain  : {"QuWhat"..="QuWhat"}
output         : 'QuWhat'


ast            : insert('Quadratic', 3, 100, NULL)
raw expr       : insert('Quadratic', 3, 100, NULL)
checked expr   : insert<String NULL, Int64 NULL, Int64 NULL, String NULL>(CAST<String>("Quadratic" AS String NULL), CAST<UInt8>(3_u8 AS Int64 NULL), CAST<UInt8>(100_u8 AS Int64 NULL), CAST<NULL>(NULL AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : insert('Quadratic', 3, NULL, 'NULL')
raw expr       : insert('Quadratic', 3, NULL, 'NULL')
checked expr   : insert<String NULL, Int64 NULL, Int64 NULL, String NULL>(CAST<String>("Quadratic" AS String NULL), CAST<UInt8>(3_u8 AS Int64 NULL), CAST<NULL>(NULL AS Int64 NULL), CAST<String>("NULL" AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : insert('Quadratic', NULL, 100, 'NULL')
raw expr       : insert('Quadratic', NULL, 100, 'NULL')
checked expr   : insert<String NULL, Int64 NULL, Int64 NULL, String NULL>(CAST<String>("Quadratic" AS String NULL), CAST<NULL>(NULL AS Int64 NULL), CAST<UInt8>(100_u8 AS Int64 NULL), CAST<String>("NULL" AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : insert('你好世界', 1, 2, 'こんにちは')
raw expr       : insert('你好世界', 1, 2, 'こんにちは')
checked expr   : insert<String, Int64, Int64, String>("你好世界", CAST<UInt8>(1_u8 AS Int64), CAST<UInt8>(2_u8 AS Int64), "こんにちは")
optimized expr : "こんにちは世界"
output type    : String
output domain  : {"こんにちは世界"..="こんにちは世界"}
output         : 'こんにちは世界'


ast            : insert(NULL, 2, 100, 'NULL')
raw expr       : insert(NULL, 2, 100, 'NULL')
checked expr   : insert<String NULL, Int64 NULL, Int64 NULL, String NULL>(CAST<NULL>(NULL AS String NULL), CAST<UInt8>(2_u8 AS Int64 NULL), CAST<UInt8>(100_u8 AS Int64 NULL), CAST<String>("NULL" AS String NULL))
optimized expr : NULL
output type    : String NULL
output domain  : {NULL}
output         : NULL


ast            : insert(a, b, c, d)
raw expr       : insert(a::String, b::UInt8, c::UInt8, d::String)
checked expr   : insert<String, Int64, Int64, String>(a, CAST<UInt8>(b AS Int64), CAST<UInt8>(c AS Int64), d)
evaluation:
+--------+-----------------+---------+---------+---------------+---------+
|        | a               | b       | c       | d             | Output  |
+--------+-----------------+---------+---------+---------------+---------+
| Type   | String          | UInt8   | UInt8   | String        | String  |
| Domain | {"cc"..="test"} | {1..=4} | {1..=5} | {"12"..="zc"} | {""..}  |
| Row 0  | 'hi'            | 1       | 3       | 'xx'          | 'xx'    |
| Row 1  | 'test'          | 4       | 5       | 'zc'          | 'teszc' |
| Row 2  | 'cc'            | 1       | 1       | '12'          | '12c'   |
| Row 3  | 'q'             | 1       | 1       | '56'          | '56'    |
+--------+-----------------+---------+---------+---------------+---------+
evaluation (internal):
+--------+----------------------------------+
| Column | Data                             |
+--------+----------------------------------+
| a      | StringColumn[hi, test, cc, q]    |
| b      | UInt8([1, 4, 1, 1])              |
| c      | UInt8([3, 5, 1, 1])              |
| d      | StringColumn[xx, zc, 12, 56]     |
| Output | StringColumn[xx, teszc, 12c, 56] |
+--------+----------------------------------+


ast            : insert(x, y, z, u)
raw expr       : insert(x::String NULL, y::UInt8 NULL, z::UInt8 NULL, u::String NULL)
checked expr   : insert<String NULL, Int64 NULL, Int64 NULL, String NULL>(x, CAST<UInt8 NULL>(y AS Int64 NULL), CAST<UInt8 NULL>(z AS Int64 NULL), u)
evaluation:
+--------+--------------------------+------------------+------------------+------------------------+-----------------+
|        | x                        | y                | z                | u                      | Output          |
+--------+--------------------------+------------------+------------------+------------------------+-----------------+
| Type   | String NULL              | UInt8 NULL       | UInt8 NULL       | String NULL            | String NULL     |
| Domain | {"cc"..="test"} ∪ {NULL} | {1..=4} ∪ {NULL} | {1..=3} ∪ {NULL} | {"12"..="zc"} ∪ {NULL} | {""..} ∪ {NULL} |
| Row 0  | NULL                     | 1                | 3                | NULL                   | NULL            |
| Row 1  | 'test'                   | 4                | NULL             | 'zc'                   | NULL            |
| Row 2  | 'cc'                     | NULL             | 1                | '12'                   | NULL            |
| Row 3  | 'q'                      | 1                | 1                | '56'                   | '56'            |
+--------+--------------------------+------------------+------------------+------------------------+-----------------+
evaluation (internal):
+--------+-------------------------------------------------------------------------------------+
| Column | Data                                                                                |
+--------+-------------------------------------------------------------------------------------+
| x      | NullableColumn { column: StringColumn[hi, test, cc, q], validity: [0b____1110] }    |
| y      | NullableColumn { column: UInt8([1, 4, 1, 1]), validity: [0b____1011] }              |
| z      | NullableColumn { column: UInt8([3, 5, 1, 1]), validity: [0b____1101] }              |
| u      | NullableColumn { column: StringColumn[xx, zc, 12, 56], validity: [0b____1110] }     |
| Output | NullableColumn { column: StringColumn[xx, teszc, 12c, 56], validity: [0b____1000] } |
+--------+-------------------------------------------------------------------------------------+


ast            : space(0)
raw expr       : space(0)
checked expr   : space<UInt64>(CAST<UInt8>(0_u8 AS UInt64))
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : space(5)
raw expr       : space(5)
checked expr   : space<UInt64>(CAST<UInt8>(5_u8 AS UInt64))
optimized expr : "     "
output type    : String
output domain  : {"     "..="     "}
output         : '     '


error: 
  --> SQL:1:1
  |
1 | space(2000000)
  | ^^^^^^^^^^^^^^ space length is too big, max is: 1000000 while evaluating function `space(2000000)` in expr `space(CAST(2000000 AS UInt64))`



ast            : space(a)
raw expr       : space(a::UInt8)
checked expr   : space<UInt64>(CAST<UInt8>(a AS UInt64))
evaluation:
+--------+---------+-------------+
|        | a       | Output      |
+--------+---------+-------------+
| Type   | UInt8   | String      |
| Domain | {0..=9} | Unknown     |
| Row 0  | 0       | ''          |
| Row 1  | 1       | ' '         |
| Row 2  | 2       | '  '        |
| Row 3  | 3       | '   '       |
| Row 4  | 4       | '    '      |
| Row 5  | 5       | '     '     |
| Row 6  | 6       | '      '    |
| Row 7  | 7       | '       '   |
| Row 8  | 8       | '        '  |
| Row 9  | 9       | '         ' |
+--------+---------+-------------+
evaluation (internal):
+--------+-------------------------------------------------------------------------------+
| Column | Data                                                                          |
+--------+-------------------------------------------------------------------------------+
| a      | UInt8([0, 1, 2, 3, 4, 5, 6, 7, 8, 9])                                         |
| Output | StringColumn[,  ,   ,    ,     ,      ,       ,        ,         ,          ] |
+--------+-------------------------------------------------------------------------------+


ast            : left('', 0)
raw expr       : left('', 0)
checked expr   : left<String, UInt64>("", CAST<UInt8>(0_u8 AS UInt64))
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : left('', 1)
raw expr       : left('', 1)
checked expr   : left<String, UInt64>("", CAST<UInt8>(1_u8 AS UInt64))
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : left('123456789', a)
raw expr       : left('123456789', a::UInt8)
checked expr   : left<String, UInt64>("123456789", CAST<UInt8>(a AS UInt64))
evaluation:
+--------+----------+--------------------+
|        | a        | Output             |
+--------+----------+--------------------+
| Type   | UInt8    | String             |
| Domain | {0..=10} | {""..="123456789"} |
| Row 0  | 0        | ''                 |
| Row 1  | 1        | '1'                |
| Row 2  | 2        | '12'               |
| Row 3  | 3        | '123'              |
| Row 4  | 4        | '1234'             |
| Row 5  | 5        | '12345'            |
| Row 6  | 6        | '123456'           |
| Row 7  | 7        | '1234567'          |
| Row 8  | 8        | '12345678'         |
| Row 9  | 9        | '123456789'        |
| Row 10 | 10       | '123456789'        |
+--------+----------+--------------------+
evaluation (internal):
+--------+------------------------------------------------------------------------------------------+
| Column | Data                                                                                     |
+--------+------------------------------------------------------------------------------------------+
| a      | UInt8([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10])                                                |
| Output | StringColumn[, 1, 12, 123, 1234, 12345, 123456, 1234567, 12345678, 123456789, 123456789] |
+--------+------------------------------------------------------------------------------------------+


ast            : right('', 0)
raw expr       : right('', 0)
checked expr   : right<String, UInt64>("", CAST<UInt8>(0_u8 AS UInt64))
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : right('', 1)
raw expr       : right('', 1)
checked expr   : right<String, UInt64>("", CAST<UInt8>(1_u8 AS UInt64))
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : right('123456789', a)
raw expr       : right('123456789', a::UInt8)
checked expr   : right<String, UInt64>("123456789", CAST<UInt8>(a AS UInt64))
evaluation:
+--------+----------+-------------+
|        | a        | Output      |
+--------+----------+-------------+
| Type   | UInt8    | String      |
| Domain | {0..=10} | {""..}      |
| Row 0  | 0        | ''          |
| Row 1  | 1        | '9'         |
| Row 2  | 2        | '89'        |
| Row 3  | 3        | '789'       |
| Row 4  | 4        | '6789'      |
| Row 5  | 5        | '56789'     |
| Row 6  | 6        | '456789'    |
| Row 7  | 7        | '3456789'   |
| Row 8  | 8        | '23456789'  |
| Row 9  | 9        | '123456789' |
| Row 10 | 10       | '123456789' |
+--------+----------+-------------+
evaluation (internal):
+--------+------------------------------------------------------------------------------------------+
| Column | Data                                                                                     |
+--------+------------------------------------------------------------------------------------------+
| a      | UInt8([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10])                                                |
| Output | StringColumn[, 9, 89, 789, 6789, 56789, 456789, 3456789, 23456789, 123456789, 123456789] |
+--------+------------------------------------------------------------------------------------------+


ast            : mid('1234567890', -3, 3)
raw expr       : mid('1234567890', minus(3), 3)
checked expr   : substr<String, Int64, UInt64>("1234567890", CAST<Int16>(minus<UInt8>(3_u8) AS Int64), CAST<UInt8>(3_u8 AS UInt64))
optimized expr : "890"
output type    : String
output domain  : {"890"..="890"}
output         : '890'


ast            : mid('1234567890', -3, 4 - 1)
raw expr       : mid('1234567890', minus(3), minus(4, 1))
checked expr   : substr<String, Int64, UInt64>("1234567890", CAST<Int16>(minus<UInt8>(3_u8) AS Int64), CAST<Int16>(minus<UInt8, UInt8>(4_u8, 1_u8) AS UInt64))
optimized expr : "890"
output type    : String
output domain  : {"890"..="890"}
output         : '890'


ast            : mid('1234567890', -3)
raw expr       : mid('1234567890', minus(3))
checked expr   : substr<String, Int64>("1234567890", CAST<Int16>(minus<UInt8>(3_u8) AS Int64))
optimized expr : "890"
output type    : String
output domain  : {"890"..="890"}
output         : '890'


ast            : substring('', 0, 1)
raw expr       : substr('', 0, 1)
checked expr   : substr<String, Int64, UInt64>("", CAST<UInt8>(0_u8 AS Int64), CAST<UInt8>(1_u8 AS UInt64))
optimized expr : ""
output type    : String
output domain  : {""..=""}
output         : ''


ast            : substr('Sakila' from -4 for 2)
raw expr       : substr('Sakila', minus(4), 2)
checked expr   : substr<String, Int64, UInt64>("Sakila", CAST<Int16>(minus<UInt8>(4_u8) AS Int64), CAST<UInt8>(2_u8 AS UInt64))
optimized expr : "ki"
output type    : String
output domain  : {"ki"..="ki"}
output         : 'ki'


ast            : substr('sakila' FROM -4)
raw expr       : substr('sakila', minus(4))
checked expr   : substr<String, Int64>("sakila", CAST<Int16>(minus<UInt8>(4_u8) AS Int64))
optimized expr : "kila"
output type    : String
output domain  : {"kila"..="kila"}
output         : 'kila'


ast            : substr('abc',2)
raw expr       : substr('abc', 2)
checked expr   : substr<String, Int64>("abc", CAST<UInt8>(2_u8 AS Int64))
optimized expr : "bc"
output type    : String
output domain  : {"bc"..="bc"}
output         : 'bc'


ast            : substr('你好世界', 3)
raw expr       : substr('你好世界', 3)
checked expr   : substr<String, Int64>("你好世界", CAST<UInt8>(3_u8 AS Int64))
optimized expr : "世界"
output type    : String
output domain  : {"世界"..="世界"}
output         : '世界'


ast            : substr('こんにちは', 2)
raw expr       : substr('こんにちは', 2)
checked expr   : substr<String, Int64>("こんにちは", CAST<UInt8>(2_u8 AS Int64))
optimized expr : "んにちは"
output type    : String
output domain  : {"んにちは"..="んにちは"}
output         : 'んにちは'


ast            : substr('abc', pos, len)
raw expr       : substr('abc', pos::Int8, len::UInt8)
checked expr   : substr<String, Int64, UInt64>("abc", CAST<Int8>(pos AS Int64), CAST<UInt8>(len AS UInt64))
evaluation:
+--------+----------+---------+--------+
|        | pos      | len     | Output |
+--------+----------+---------+--------+
| Type   | Int8     | UInt8   | String |
| Domain | {-4..=4} | {0..=4} | {""..} |
| Row 0  | 0        | 0       | ''     |
| Row 1  | 0        | 1       | ''     |
| Row 2  | 0        | 2       | ''     |
| Row 3  | 0        | 3       | ''     |
| Row 4  | 0        | 4       | ''     |
| Row 5  | 1        | 0       | ''     |
| Row 6  | 1        | 1       | 'a'    |
| Row 7  | 1        | 2       | 'ab'   |
| Row 8  | 1        | 3       | 'abc'  |
| Row 9  | 1        | 4       | 'abc'  |
| Row 10 | 2        | 0       | ''     |
| Row 11 | 2        | 1       | 'b'    |
| Row 12 | 2        | 2       | 'bc'   |
| Row 13 | 2        | 3       | 'bc'   |
| Row 14 | 2        | 4       | 'bc'   |
| Row 15 | 3        | 0       | ''     |
| Row 16 | 3        | 1       | 'c'    |
| Row 17 | 3        | 2       | 'c'    |
| Row 18 | 3        | 3       | 'c'    |
| Row 19 | 3        | 4       | 'c'    |
| Row 20 | 4        | 0       | ''     |
| Row 21 | 4        | 1       | ''     |
| Row 22 | 4        | 2       | ''     |
| Row 23 | 4        | 3       | ''     |
| Row 24 | 4        | 4       | ''     |
| Row 25 | -1       | 0       | ''     |
| Row 26 | -1       | 1       | 'c'    |
| Row 27 | -1       | 2       | 'c'    |
| Row 28 | -1       | 3       | 'c'    |
| Row 29 | -1       | 4       | 'c'    |
| Row 30 | -2       | 0       | ''     |
| Row 31 | -2       | 1       | 'b'    |
| Row 32 | -2       | 2       | 'bc'   |
| Row 33 | -2       | 3       | 'bc'   |
| Row 34 | -2       | 4       | 'bc'   |
| Row 35 | -3       | 0       | ''     |
| Row 36 | -3       | 1       | 'a'    |
| Row 37 | -3       | 2       | 'ab'   |
| Row 38 | -3       | 3       | 'abc'  |
| Row 39 | -3       | 4       | 'abc'  |
| Row 40 | -4       | 0       | ''     |
| Row 41 | -4       | 1       | ''     |
| Row 42 | -4       | 2       | ''     |
| Row 43 | -4       | 3       | ''     |
| Row 44 | -4       | 4       | ''     |
+--------+----------+---------+--------+
evaluation (internal):
+--------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Column | Data                                                                                                                                                              |
+--------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| pos    | Int8([0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 4, 4, 4, 4, 4, -1, -1, -1, -1, -1, -2, -2, -2, -2, -2, -3, -3, -3, -3, -3, -4, -4, -4, -4, -4]) |
| len    | UInt8([0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 0, 1, 2, 3, 4, 0, 1, 2, 3, 4])                    |
| Output | StringColumn[, , , , , , a, ab, abc, abc, , b, bc, bc, bc, , c, c, c, c, , , , , , , c, c, c, c, , b, bc, bc, bc, , a, ab, abc, abc, , , , , ]                    |
+--------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+


ast            : split('Sakila', 'il')
raw expr       : split('Sakila', 'il')
checked expr   : split<String, String>("Sakila", "il")
optimized expr : ['Sak', 'a']
output type    : Array(String)
output domain  : [{"Sak"..="a"}]
output         : ['Sak', 'a']


ast            : split('sakila', 'a')
raw expr       : split('sakila', 'a')
checked expr   : split<String, String>("sakila", "a")
optimized expr : ['s', 'kil', '']
output type    : Array(String)
output domain  : [{""..="s"}]
output         : ['s', 'kil', '']


ast            : split('abc','b')
raw expr       : split('abc', 'b')
checked expr   : split<String, String>("abc", "b")
optimized expr : ['a', 'c']
output type    : Array(String)
output domain  : [{"a"..="c"}]
output         : ['a', 'c']


ast            : split('你好世界', '好')
raw expr       : split('你好世界', '好')
checked expr   : split<String, String>("你好世界", "好")
optimized expr : ['你', '世界']
output type    : Array(String)
output domain  : [{"世界"..="你"}]
output         : ['你', '世界']


ast            : split('こんにちは', 'に')
raw expr       : split('こんにちは', 'に')
checked expr   : split<String, String>("こんにちは", "に")
optimized expr : ['こん', 'ちは']
output type    : Array(String)
output domain  : [{"こん"..="ちは"}]
output         : ['こん', 'ちは']


ast            : split(str, sep)
raw expr       : split(str::String NULL, sep::String NULL)
checked expr   : split<String NULL, String NULL>(str, sep)
evaluation:
+--------+---------------------------------------+------------------------+---------------------------+
|        | str                                   | sep                    | Output                    |
+--------+---------------------------------------+------------------------+---------------------------+
| Type   | String NULL                           | String NULL            | Array(String) NULL        |
| Domain | {"aaa--bbb-BBB--ccc"..="cc"} ∪ {NULL} | {"--"..="ee"} ∪ {NULL} | [{""..}] ∪ {NULL}         |
| Row 0  | NULL                                  | NULL                   | NULL                      |
| Row 1  | 'aaa--bbb-BBB--ccc'                   | '--'                   | ['aaa', 'bbb-BBB', 'ccc'] |
| Row 2  | 'cc'                                  | 'cc'                   | ['']                      |
| Row 3  | 'aeeceedeef'                          | 'ee'                   | ['a', 'c', 'd', 'f']      |
+--------+---------------------------------------+------------------------+---------------------------+
evaluation (internal):
+--------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Column | Data                                                                                                                                                              |
+--------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| str    | NullableColumn { column: StringColumn[127.0.0.1, aaa--bbb-BBB--ccc, cc, aeeceedeef], validity: [0b____1110] }                                                     |
| sep    | NullableColumn { column: StringColumn[., --, cc, ee], validity: [0b____1110] }                                                                                    |
| Output | NullableColumn { column: ArrayColumn { values: StringColumn[127, 0, 0, 1, aaa, bbb-BBB, ccc, , a, c, d, f], offsets: [0, 4, 7, 8, 12] }, validity: [0b____1110] } |
+--------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------+


ast            : to_uuid(5::decimal(1,0))
raw expr       : to_uuid(CAST(5 AS Decimal(1, 0)))
checked expr   : to_uuid<Decimal(1, 0)>(CAST<UInt8>(5_u8 AS Decimal(1, 0)))
optimized expr : "00000000000000000000000000000005"
output type    : String
output domain  : {"00000000000000000000000000000005"..="00000000000000000000000000000005"}
output         : '00000000000000000000000000000005'


ast            : to_uuid(a)
raw expr       : to_uuid(a::Decimal(10, 0))
checked expr   : to_uuid<Decimal(10, 0)>(a)
evaluation:
+--------+----------------+------------------------------------+
|        | a              | Output                             |
+--------+----------------+------------------------------------+
| Type   | Decimal(10, 0) | String                             |
| Domain | {0..=2}        | {""..}                             |
| Row 0  | 0              | '00000000000000000000000000000000' |
| Row 1  | 1              | '00000000000000000000000000000001' |
| Row 2  | 2              | '00000000000000000000000000000002' |
+--------+----------------+------------------------------------+
evaluation (internal):
+--------+--------------------------------------------------------------------------------------------------------------------+
| Column | Data                                                                                                               |
+--------+--------------------------------------------------------------------------------------------------------------------+
| a      | Decimal128([0, 1, 2])                                                                                              |
| Output | StringColumn[00000000000000000000000000000000, 00000000000000000000000000000001, 00000000000000000000000000000002] |
+--------+--------------------------------------------------------------------------------------------------------------------+


ast            : to_uuid(a)
raw expr       : to_uuid(a::Decimal(10, 0) NULL)
checked expr   : to_uuid<Decimal(10, 0) NULL>(a)
evaluation:
+--------+---------------------+------------------------------------+
|        | a                   | Output                             |
+--------+---------------------+------------------------------------+
| Type   | Decimal(10, 0) NULL | String NULL                        |
| Domain | {0..=2}             | {""..} ∪ {NULL}                    |
| Row 0  | 0                   | '00000000000000000000000000000000' |
| Row 1  | 1                   | '00000000000000000000000000000001' |
| Row 2  | 2                   | '00000000000000000000000000000002' |
+--------+---------------------+------------------------------------+
evaluation (internal):
+--------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Column | Data                                                                                                                                                                  |
+--------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| a      | NullableColumn { column: Decimal128([0, 1, 2]), validity: [0b_____111] }                                                                                              |
| Output | NullableColumn { column: StringColumn[00000000000000000000000000000000, 00000000000000000000000000000001, 00000000000000000000000000000002], validity: [0b_____111] } |
+--------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------+


ast            : to_uuid(a)
raw expr       : to_uuid(a::Decimal(10, 0))
checked expr   : to_uuid<Decimal(10, 0)>(a)
evaluation:
+--------+----------------+------------------------------------+
|        | a              | Output                             |
+--------+----------------+------------------------------------+
| Type   | Decimal(10, 0) | String                             |
| Domain | {0..=20}       | {""..}                             |
| Row 0  | 0              | '00000000000000000000000000000000' |
| Row 1  | 20             | '00000000000000000000000000000014' |
+--------+----------------+------------------------------------+
evaluation (internal):
+--------+----------------------------------------------------------------------------------+
| Column | Data                                                                             |
+--------+----------------------------------------------------------------------------------+
| a      | Decimal256([0, 20])                                                              |
| Output | StringColumn[00000000000000000000000000000000, 00000000000000000000000000000014] |
+--------+----------------------------------------------------------------------------------+


ast            : to_uuid(a)
raw expr       : to_uuid(a::Decimal(10, 0) NULL)
checked expr   : to_uuid<Decimal(10, 0) NULL>(a)
evaluation:
+--------+---------------------+------------------------------------+
|        | a                   | Output                             |
+--------+---------------------+------------------------------------+
| Type   | Decimal(10, 0) NULL | String NULL                        |
| Domain | {0..=20}            | {""..} ∪ {NULL}                    |
| Row 0  | 0                   | '00000000000000000000000000000000' |
| Row 1  | 20                  | '00000000000000000000000000000014' |
+--------+---------------------+------------------------------------+
evaluation (internal):
+--------+-------------------------------------------------------------------------------------------------------------------------------------+
| Column | Data                                                                                                                                |
+--------+-------------------------------------------------------------------------------------------------------------------------------------+
| a      | NullableColumn { column: Decimal256([0, 20]), validity: [0b______11] }                                                              |
| Output | NullableColumn { column: StringColumn[00000000000000000000000000000000, 00000000000000000000000000000014], validity: [0b______11] } |
+--------+-------------------------------------------------------------------------------------------------------------------------------------+


error: 
  --> SQL:1:1
  |
1 | to_uuid(a)
  | ^^^^^^^^^^ no function matches signature `to_uuid(Decimal(40, 0))`, you might need to add explicit type casts.



